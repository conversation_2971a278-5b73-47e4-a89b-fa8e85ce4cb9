{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "alwaysAllow": ["resolve-library-id", "get-library-docs"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:/EServer/core/www/newtheme/wp-content/themes/kuang/"], "alwaysAllow": ["read_file", "read_multiple_files", "write_file", "edit_file", "list_directory", "list_directory_with_sizes", "directory_tree", "search_files", "get_file_info", "list_allowed_directories"]}}}