/**
 * XUN Accordion Field JavaScript
 * 
 * 现代化的手风琴字段交互逻辑
 * 支持流畅动画、键盘导航、无障碍访问等高级功能
 * 比CSF更好的用户体验
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Accordion Field 类
     */
    var XunAccordionField = {
        
        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
            this.initializeFields();
            this.setupKeyboardNavigation();
        },

        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;

            // 点击触发器展开/折叠
            $(document).on('click', '.xun-accordion-trigger', function(e) {
                e.preventDefault();
                self.toggleAccordion($(this));
            });

            // 键盘导航支持
            $(document).on('keydown', '.xun-accordion-trigger', function(e) {
                self.handleKeyboardNavigation(e, $(this));
            });

            // 窗口大小改变时重新计算高度
            $(window).on('resize', function() {
                self.recalculateHeights();
            });

            // 监听页面切换和可见性变化 - 修复页面切换问题
            $(document).on('visibilitychange', function() {
                if (!document.hidden) {
                    // 页面变为可见时重新初始化
                    setTimeout(function() {
                        self.reinitializeFields();
                    }, 100);
                }
            });

            // 监听窗口焦点事件
            $(window).on('focus', function() {
                setTimeout(function() {
                    self.reinitializeFields();
                }, 100);
            });
        },

        /**
         * 初始化所有手风琴字段
         */
        initializeFields: function() {
            $('.xun-accordion-field').each(function() {
                var $field = $(this);
                XunAccordionField.initializeField($field);
            });
        },

        /**
         * 重新初始化所有字段 - 用于页面切换后的修复
         */
        reinitializeFields: function() {
            var self = this;

            $('.xun-accordion-field').each(function() {
                var $field = $(this);

                $field.find('.xun-accordion-item').each(function() {
                    var $item = $(this);
                    var $trigger = $item.find('.xun-accordion-trigger');
                    var $content = $item.find('.xun-accordion-content');

                    // 检查是否应该展开
                    var shouldBeOpen = $trigger.data('default-open') === true ||
                                      $trigger.data('default-open') === 'true' ||
                                      $content.data('default-open') === true ||
                                      $content.data('default-open') === 'true' ||
                                      $content.hasClass('xun-accordion-open');

                    var isCurrentlyOpen = $trigger.attr('aria-expanded') === 'true';

                    // 如果状态不匹配，修复它
                    if (shouldBeOpen && !isCurrentlyOpen) {
                        self.openAccordion($trigger);
                    } else if (!shouldBeOpen && isCurrentlyOpen) {
                        self.closeAccordion($trigger);
                    }
                });
            });
        },

        /**
         * 初始化单个手风琴字段 - 修复默认展开状态问题
         */
        initializeField: function($field) {
            var multiple = $field.data('multiple');
            var collapsible = $field.data('collapsible');

            // 设置初始状态 - 使用更可靠的方法
            $field.find('.xun-accordion-item').each(function(index) {
                var $item = $(this);
                var $trigger = $item.find('.xun-accordion-trigger');
                var $content = $item.find('.xun-accordion-content');
                var $icon = $trigger.find('.xun-accordion-icon');

                // 检查默认展开状态 - 多种方式确保准确性
                var isDefaultOpen = $trigger.data('default-open') === true ||
                                   $trigger.data('default-open') === 'true' ||
                                   $content.data('default-open') === true ||
                                   $content.data('default-open') === 'true' ||
                                   $content.hasClass('xun-accordion-open') ||
                                   !$content.hasClass('max-h-0');

                // 设置ARIA属性
                $trigger.attr('aria-expanded', isDefaultOpen);
                $content.attr('aria-hidden', !isDefaultOpen);

                // 设置正确的状态
                if (isDefaultOpen) {
                    // 展开状态
                    $icon.addClass('rotate-180');
                    $item.addClass('xun-accordion-open');
                    $content.removeClass('max-h-0');
                    $content.addClass('xun-accordion-open');

                    // 确保内容可见 - 延迟执行以确保DOM完全渲染
                    setTimeout(function() {
                        if ($content.is(':visible')) {
                            var contentHeight = $content[0].scrollHeight;
                            if (contentHeight > 0) {
                                $content.css({
                                    'max-height': 'none',
                                    'opacity': '1'
                                });
                            } else {
                                // 如果高度为0，强制显示
                                $content.css({
                                    'max-height': 'none',
                                    'opacity': '1',
                                    'display': 'block'
                                });
                            }
                        }
                    }, 50);
                } else {
                    // 折叠状态
                    $icon.removeClass('rotate-180');
                    $item.removeClass('xun-accordion-open');
                    $content.addClass('max-h-0');
                    $content.removeClass('xun-accordion-open');
                    $content.css({
                        'max-height': '0',
                        'opacity': '0'
                    });
                }
            });

            // 如果不允许多个展开，确保只有一个项目展开
            if (!multiple) {
                var $openItems = $field.find('.xun-accordion-content.xun-accordion-open');
                if ($openItems.length > 1) {
                    $openItems.slice(1).each(function() {
                        XunAccordionField.closeAccordion($(this).siblings('.xun-accordion-trigger'));
                    });
                }
            }
        },

        /**
         * 切换手风琴状态
         */
        toggleAccordion: function($trigger) {
            var $field = $trigger.closest('.xun-accordion-field');
            var $item = $trigger.closest('.xun-accordion-item');
            var $content = $item.find('.xun-accordion-content');
            var $icon = $trigger.find('.xun-accordion-icon');
            var isOpen = $trigger.attr('aria-expanded') === 'true';
            var multiple = $field.data('multiple');
            var collapsible = $field.data('collapsible');

            // 如果不允许多个展开，先关闭其他项目
            if (!multiple && !isOpen) {
                $field.find('.xun-accordion-trigger').each(function() {
                    var $otherTrigger = $(this);
                    if ($otherTrigger[0] !== $trigger[0] && $otherTrigger.attr('aria-expanded') === 'true') {
                        XunAccordionField.closeAccordion($otherTrigger);
                    }
                });
            }

            // 切换当前项目
            if (isOpen) {
                if (collapsible) {
                    this.closeAccordion($trigger);
                }
            } else {
                this.openAccordion($trigger);
            }

            // 触发自定义事件
            this.triggerEvent($field, 'accordion:toggled', {
                trigger: $trigger,
                item: $item,
                isOpen: !isOpen
            });
        },

        /**
         * 打开手风琴项目 - 改进的展开逻辑
         */
        openAccordion: function($trigger) {
            var $item = $trigger.closest('.xun-accordion-item');
            var $content = $item.find('.xun-accordion-content');
            var $icon = $trigger.find('.xun-accordion-icon');

            // 更新ARIA属性
            $trigger.attr('aria-expanded', 'true');
            $content.attr('aria-hidden', 'false');

            // 移除折叠类，添加展开类
            $content.removeClass('max-h-0');
            $content.addClass('xun-accordion-open');

            // 设置展开样式 - 使用更可靠的方法
            $content.css({
                'max-height': 'none',
                'opacity': '1'
            });

            // 旋转图标
            $icon.addClass('rotate-180');

            // 添加打开状态类
            $item.addClass('xun-accordion-open');

            // 触发打开事件
            this.triggerEvent($trigger.closest('.xun-accordion-field'), 'accordion:opened', {
                trigger: $trigger,
                item: $item
            });
        },

        /**
         * 关闭手风琴项目 - 改进的折叠逻辑
         */
        closeAccordion: function($trigger) {
            var $item = $trigger.closest('.xun-accordion-item');
            var $content = $item.find('.xun-accordion-content');
            var $icon = $trigger.find('.xun-accordion-icon');

            // 更新ARIA属性
            $trigger.attr('aria-expanded', 'false');
            $content.attr('aria-hidden', 'true');

            // 添加折叠类，移除展开类
            $content.addClass('max-h-0');
            $content.removeClass('xun-accordion-open');

            // 设置折叠样式
            $content.css({
                'max-height': '0',
                'opacity': '0'
            });

            // 旋转图标
            $icon.removeClass('rotate-180');

            // 移除打开状态类
            $item.removeClass('xun-accordion-open');

            // 触发关闭事件
            this.triggerEvent($trigger.closest('.xun-accordion-field'), 'accordion:closed', {
                trigger: $trigger,
                item: $item
            });
        },

        /**
         * 设置内容高度
         */
        setContentHeight: function($content) {
            // 检查是否有max-h-0类
            var hadMaxHeight = $content.hasClass('max-h-0');

            if (hadMaxHeight) {
                // 如果有max-h-0类，说明是折叠状态，临时移除来测量高度
                $content.removeClass('max-h-0');
                var contentHeight = $content[0].scrollHeight;
                $content.css('max-height', contentHeight + 'px');
                $content.addClass('max-h-0');
            } else {
                // 如果没有max-h-0类，说明是展开状态
                var currentMaxHeight = $content.css('max-height');
                if (currentMaxHeight === 'none' || currentMaxHeight === 'auto') {
                    // 如果当前是none或auto，保持不变（默认展开状态）
                    return;
                } else {
                    // 重新计算高度
                    var contentHeight = $content[0].scrollHeight;
                    $content.css('max-height', contentHeight + 'px');
                }
            }
        },

        /**
         * 重新计算所有打开项目的高度
         */
        recalculateHeights: function() {
            $('.xun-accordion-content:not(.max-h-0)').each(function() {
                XunAccordionField.setContentHeight($(this));
            });
        },

        /**
         * 设置键盘导航
         */
        setupKeyboardNavigation: function() {
            // 为每个手风琴字段设置键盘导航
            $('.xun-accordion-field').each(function() {
                var $field = $(this);
                var $triggers = $field.find('.xun-accordion-trigger');
                
                // 设置tabindex
                $triggers.each(function(index) {
                    $(this).attr('tabindex', '0');
                });
            });
        },

        /**
         * 处理键盘导航
         */
        handleKeyboardNavigation: function(e, $trigger) {
            var $field = $trigger.closest('.xun-accordion-field');
            var $triggers = $field.find('.xun-accordion-trigger');
            var currentIndex = $triggers.index($trigger);

            switch (e.which) {
                case 13: // Enter
                case 32: // Space
                    e.preventDefault();
                    this.toggleAccordion($trigger);
                    break;

                case 38: // Arrow Up
                    e.preventDefault();
                    var prevIndex = currentIndex > 0 ? currentIndex - 1 : $triggers.length - 1;
                    $triggers.eq(prevIndex).focus();
                    break;

                case 40: // Arrow Down
                    e.preventDefault();
                    var nextIndex = currentIndex < $triggers.length - 1 ? currentIndex + 1 : 0;
                    $triggers.eq(nextIndex).focus();
                    break;

                case 36: // Home
                    e.preventDefault();
                    $triggers.first().focus();
                    break;

                case 35: // End
                    e.preventDefault();
                    $triggers.last().focus();
                    break;
            }
        },

        /**
         * 展开所有项目
         */
        expandAll: function($field) {
            var multiple = $field.data('multiple');
            
            if (multiple) {
                $field.find('.xun-accordion-trigger').each(function() {
                    var $trigger = $(this);
                    if ($trigger.attr('aria-expanded') !== 'true') {
                        XunAccordionField.openAccordion($trigger);
                    }
                });

                // 触发展开全部事件
                this.triggerEvent($field, 'accordion:expandedAll');
            }
        },

        /**
         * 折叠所有项目
         */
        collapseAll: function($field) {
            var collapsible = $field.data('collapsible');
            
            if (collapsible) {
                $field.find('.xun-accordion-trigger').each(function() {
                    var $trigger = $(this);
                    if ($trigger.attr('aria-expanded') === 'true') {
                        XunAccordionField.closeAccordion($trigger);
                    }
                });

                // 触发折叠全部事件
                this.triggerEvent($field, 'accordion:collapsedAll');
            }
        },

        /**
         * 触发自定义事件
         */
        triggerEvent: function($field, eventName, data) {
            var fieldId = $field.data('field-id');
            $field.trigger('xun:accordion:' + eventName, $.extend({
                fieldId: fieldId
            }, data || {}));
        },

        /**
         * 获取字段状态
         */
        getFieldState: function(fieldId) {
            var $field = $('.xun-accordion-field[data-field-id="' + fieldId + '"]');
            var state = {
                fieldId: fieldId,
                items: []
            };

            $field.find('.xun-accordion-item').each(function(index) {
                var $item = $(this);
                var $trigger = $item.find('.xun-accordion-trigger');
                var isOpen = $trigger.attr('aria-expanded') === 'true';

                state.items.push({
                    index: index,
                    isOpen: isOpen,
                    title: $trigger.find('span').text().trim()
                });
            });

            return state;
        },

        /**
         * 设置字段状态
         */
        setFieldState: function(fieldId, state) {
            var $field = $('.xun-accordion-field[data-field-id="' + fieldId + '"]');
            
            if (state.items && Array.isArray(state.items)) {
                state.items.forEach(function(itemState, index) {
                    var $trigger = $field.find('.xun-accordion-trigger').eq(index);
                    if ($trigger.length) {
                        var currentlyOpen = $trigger.attr('aria-expanded') === 'true';
                        if (itemState.isOpen && !currentlyOpen) {
                            XunAccordionField.openAccordion($trigger);
                        } else if (!itemState.isOpen && currentlyOpen) {
                            XunAccordionField.closeAccordion($trigger);
                        }
                    }
                });
            }
        },

        /**
         * 重新初始化字段（用于动态添加的字段）
         */
        reinit: function() {
            this.initializeFields();
            this.setupKeyboardNavigation();
        }
    };

    // 文档就绪时初始化
    $(document).ready(function() {
        // 延迟一点执行初始化，确保所有元素都已正确渲染
        setTimeout(function() {
            XunAccordionField.init();
        }, 100);
    });

    // 暴露到全局
    window.XunAccordionField = XunAccordionField;

})(jQuery);
