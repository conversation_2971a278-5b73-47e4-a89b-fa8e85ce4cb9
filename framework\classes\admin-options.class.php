<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 后台选项类
 * 
 * 这个类负责创建和管理WordPress后台的选项页面。
 * 它提供了完整的选项面板功能，包括表单渲染、数据保存和验证。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Options' ) ) {
    
    /**
     * XUN_Options 后台选项类
     * 
     * 管理WordPress后台选项页面，包括：
     * - 菜单创建
     * - 表单渲染
     * - 数据保存
     * - 选项验证
     * 
     * @since 1.0
     */
    class XUN_Options extends XUN_Abstract {
        
        /**
         * 唯一标识符
         * 
         * @since 1.0
         * @var string
         */
        public $unique = '';
        
        /**
         * 抽象类型标识
         * 
         * @since 1.0
         * @var string
         */
        public $abstract = 'options';
        
        /**
         * 区块配置数组
         * 
         * @since 1.0
         * @var array
         */
        public $sections = array();
        
        /**
         * 选项数据数组
         * 
         * @since 1.0
         * @var array
         */
        public $options = array();
        
        /**
         * 错误信息数组
         * 
         * @since 1.0
         * @var array
         */
        public $errors = array();
        
        /**
         * 预处理的字段数组
         * 
         * @since 1.0
         * @var array
         */
        public $pre_fields = array();
        
        /**
         * 预处理的区块数组
         * 
         * @since 1.0
         * @var array
         */
        public $pre_sections = array();
        
        /**
         * 参数配置数组
         * 
         * @since 1.0
         * @var array
         */
        public $args = array(
            
            // 框架标题
            'framework_title'         => 'Xun Framework <small>by June</small>',
            'framework_class'         => '',
            
            // 菜单设置
            'menu_title'              => '',
            'menu_slug'               => '',
            'menu_type'               => 'menu',
            'menu_capability'         => 'manage_options',
            'menu_icon'               => null,
            'menu_position'           => null,
            'menu_hidden'             => false,
            'menu_parent'             => '',
            
            // 页面设置
            'sub_menu_title'          => '',
            'sub_menu_capability'     => 'manage_options',
            'sub_menu_hidden'         => false,
            
            // 表单设置
            'show_bar_menu'           => true,
            'show_sub_menu'           => true,
            'show_in_network'         => true,
            'show_in_customizer'      => false,
            
            'show_search'             => true,
            'show_reset_all'          => true,
            'show_reset_section'      => true,
            'show_footer'             => true,
            'show_all_options'        => true,
            'show_form_warning'       => true,
            'sticky_header'           => true,
            'save_defaults'           => true,
            'ajax_save'               => true,
            'form_action'             => '',
            
            // 数据库设置
            'database'                => 'option',
            'transient_time'          => 0,
            
            // 主题设置
            'theme'                   => 'dark',
            'class'                   => '',
            
            // 默认值
            'defaults'                => array(),
        );
        
        /**
         * 构造函数
         * 
         * 初始化选项页面实例。
         * 
         * @since 1.0
         * 
         * @param string $key    唯一标识符
         * @param array  $params 参数数组
         */
        public function __construct( $key, $params = array() ) {
            
            $this->unique   = $key;
            $this->args     = apply_filters( "xun_{$this->unique}_args", wp_parse_args( $params['args'], $this->args ), $this );
            $this->sections = apply_filters( "xun_{$this->unique}_sections", $params['sections'], $this );
            
            // 预处理数据
            $this->pre_fields   = $this->pre_fields( $this->sections );
            $this->pre_sections = $this->pre_sections( $this->sections );
            
            // 获取选项数据
            $this->get_options();
            
            // 保存默认值
            $this->save_defaults();
            
            // 添加WordPress钩子
            $this->add_actions();
        }
        
        /**
         * 创建实例
         * 
         * 静态方法用于创建选项页面实例。
         * 
         * @since 1.0
         * 
         * @param string $key    唯一标识符
         * @param array  $params 参数数组
         * 
         * @return XUN_Options 选项页面实例
         */
        public static function instance( $key, $params = array() ) {
            return new self( $key, $params );
        }
        
        /**
         * 添加WordPress动作钩子
         * 
         * 注册必要的WordPress钩子来处理选项页面功能。
         * 
         * @since 1.0
         */
        public function add_actions() {

            // 添加管理菜单
            add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

            // 处理表单提交
            add_action( 'admin_init', array( $this, 'save_options_handler' ) );

            // 加载管理页面脚本
            add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_scripts' ) );

            // AJAX处理程序
            add_action( 'wp_ajax_xun_export_options', array( $this, 'ajax_export' ) );
            add_action( 'wp_ajax_xun_import_options', array( $this, 'ajax_import' ) );
            add_action( 'wp_ajax_xun_reset_current', array( $this, 'ajax_reset_current' ) );
            add_action( 'wp_ajax_xun_reset_all', array( $this, 'ajax_reset_all' ) );
        }
        
        /**
         * 添加管理菜单
         * 
         * 在WordPress后台添加选项页面菜单。
         * 
         * @since 1.0
         */
        public function add_admin_menu() {
            
            if ( ! empty( $this->args['menu_hidden'] ) ) {
                return;
            }
            
            $menu_slug = $this->args['menu_slug'];
            $menu_title = $this->args['menu_title'];
            $menu_capability = $this->args['menu_capability'];
            
            if ( $this->args['menu_type'] === 'submenu' ) {

                $menu_parent = $this->args['menu_parent'];

                add_submenu_page(
                    $menu_parent,
                    $menu_title,
                    $menu_title,
                    $menu_capability,
                    $menu_slug,
                    array( $this, 'add_options_html' )
                );

            } else {

                add_menu_page(
                    $menu_title,
                    $menu_title,
                    $menu_capability,
                    $menu_slug,
                    array( $this, 'add_options_html' ),
                    $this->args['menu_icon'],
                    $this->args['menu_position']
                );
            }

            // 注册页面到框架页面列表
            if ( class_exists( 'XUN_Setup' ) ) {
                XUN_Setup::$registered_pages[] = $menu_slug;
            }
        }
        
        /**
         * 获取选项数据
         * 
         * 从数据库中获取保存的选项值。
         * 
         * @since 1.0
         * 
         * @return array 选项数据数组
         */
        public function get_options() {

            // 如果是AJAX请求后的页面刷新，强制清除缓存
            if ( isset( $_GET['xun_refresh'] ) ) {
                wp_cache_delete( $this->unique, 'options' );
            }

            if ( $this->args['database'] === 'transient' ) {
                $this->options = get_transient( $this->unique );
            } elseif ( $this->args['database'] === 'theme_mod' ) {
                $this->options = get_theme_mod( $this->unique, array() );
            } elseif ( $this->args['database'] === 'network' ) {
                $this->options = get_site_option( $this->unique, array() );
            } else {
                $this->options = get_option( $this->unique, array() );
            }

            if ( empty( $this->options ) ) {
                $this->options = array();
            }

            return $this->options;
        }
        
        /**
         * 保存选项数据
         * 
         * 将选项数据保存到数据库中。
         * 
         * @since 1.0
         * 
         * @param array $data 要保存的数据
         */
        public function save_options( $data ) {
            
            if ( $this->args['database'] === 'transient' ) {
                set_transient( $this->unique, $data, $this->args['transient_time'] );
            } elseif ( $this->args['database'] === 'theme_mod' ) {
                set_theme_mod( $this->unique, $data );
            } elseif ( $this->args['database'] === 'network' ) {
                update_site_option( $this->unique, $data );
            } else {
                update_option( $this->unique, $data );
            }
            
            // 触发保存后的动作钩子
            do_action( "xun_{$this->unique}_saved", $data, $this );
        }
        
        /**
         * 保存默认值
         *
         * 如果启用了保存默认值选项，则将字段的默认值保存到数据库。
         *
         * @since 1.0
         */
        public function save_defaults() {

            if ( ! $this->args['save_defaults'] ) {
                return;
            }

            $defaults = array();

            foreach ( $this->pre_fields as $field ) {
                if ( ! empty( $field['id'] ) && ! empty( $field['default'] ) ) {
                    $defaults[ $field['id'] ] = $field['default'];
                }
            }

            if ( ! empty( $defaults ) ) {
                $this->options = wp_parse_args( $this->options, $defaults );
                $this->save_options( $this->options );
            }
        }

        /**
         * 处理表单提交
         *
         * 处理选项页面的表单提交和数据保存。
         *
         * @since 1.0
         */
        public function save_options_handler() {

            if ( ! isset( $_POST['xun_options_nonce'] ) || ! wp_verify_nonce( $_POST['xun_options_nonce'], 'xun_options_' . $this->unique ) ) {
                if ( isset( $_POST['xun_ajax'] ) ) {
                    wp_send_json_error( array( 'message' => '安全验证失败' ) );
                }
                return;
            }

            if ( ! current_user_can( $this->args['menu_capability'] ) ) {
                if ( isset( $_POST['xun_ajax'] ) ) {
                    wp_send_json_error( array( 'message' => '权限不足' ) );
                }
                return;
            }

            if ( isset( $_POST[ $this->unique ] ) ) {

                $data = $_POST[ $this->unique ];

                // 清理和验证数据
                $sanitized_data = $this->sanitize_data( $data, $this->pre_fields );

                // 保存数据
                $result = $this->save_options( $sanitized_data );

                // 重新获取选项
                $this->get_options();

                // AJAX响应
                if ( isset( $_POST['xun_ajax'] ) ) {
                    if ( $result !== false ) {
                        wp_send_json_success( array( 'message' => '设置已保存' ) );
                    } else {
                        wp_send_json_error( array( 'message' => '保存失败' ) );
                    }
                } else {
                    // 添加成功消息
                    add_action( 'admin_notices', array( $this, 'admin_notice_success' ) );
                }

                // 触发保存后的钩子
                do_action( "xun_{$this->unique}_saved", $sanitized_data, $this );
            }
        }

        /**
         * 显示成功消息
         *
         * 在管理页面显示保存成功的消息。
         *
         * @since 1.0
         */
        public function admin_notice_success() {
            // 不再显示WordPress默认通知，改为使用JavaScript通知
        }

        /**
         * 加载管理页面脚本
         *
         * 在选项页面加载必要的CSS和JavaScript文件。
         *
         * @since 1.0
         *
         * @param string $hook 当前页面钩子
         */
        public function admin_enqueue_scripts( $hook ) {

            // 检查是否在当前选项页面
            if ( isset( $_GET['page'] ) && $_GET['page'] === $this->args['menu_slug'] ) {

                // 动态计算框架资源URL
                $framework_url = $this->get_framework_url();

                // 加载弹窗组件
                wp_enqueue_script(
                    'xun-dialog',
                    $framework_url . 'assets/js/dialog.js',
                    array(),
                    '1.0',
                    true
                );

                // 加载框架脚本
                wp_enqueue_script(
                    'xun-framework',
                    $framework_url . 'assets/js/framework.min.js',
                    array( 'jquery', 'xun-dialog' ),
                    '1.0',
                    true
                );

                // 传递AJAX URL到前端
                wp_localize_script( 'xun-framework', 'xunAjax', array(
                    'ajaxurl' => admin_url( 'admin-ajax.php' ),
                    'nonce' => wp_create_nonce( 'xun_options_nonce' )
                ) );

                // 字段资源将在渲染时按需加载，无需预加载
            }
        }

        /**
         * 获取框架资源URL
         * 智能检测主题或插件环境
         *
         * @since 1.0
         * @return string 框架资源的基础URL
         */
        private function get_framework_url() {
            $framework_dir = XUN_DIR;

            // 标准化路径
            $framework_dir = wp_normalize_path( $framework_dir );
            $wp_content_dir = wp_normalize_path( WP_CONTENT_DIR );

            // 检测是否在wp-content目录中
            if ( strpos( $framework_dir, $wp_content_dir ) === 0 ) {
                // 计算相对于wp-content的路径
                $relative_path = substr( $framework_dir, strlen( $wp_content_dir ) );
                return WP_CONTENT_URL . $relative_path . '/';
            }

            // 检测是否在主题目录中
            $theme_dir = wp_normalize_path( get_template_directory() );
            if ( strpos( $framework_dir, $theme_dir ) === 0 ) {
                $relative_path = substr( $framework_dir, strlen( $theme_dir ) );
                return get_template_directory_uri() . $relative_path . '/';
            }

            // 检测是否在插件目录中
            $plugin_dir = wp_normalize_path( WP_PLUGIN_DIR );
            if ( strpos( $framework_dir, $plugin_dir ) === 0 ) {
                $relative_path = substr( $framework_dir, strlen( $plugin_dir ) );
                return WP_PLUGIN_URL . $relative_path . '/';
            }

            // 后备方案：使用WordPress根目录
            $abspath = wp_normalize_path( ABSPATH );
            if ( strpos( $framework_dir, $abspath ) === 0 ) {
                $relative_path = substr( $framework_dir, strlen( $abspath ) );
                return home_url( '/' . $relative_path . '/' );
            }

            // 最后的后备方案
            return plugins_url( '', XUN_FILE ) . '/';
        }

        /**
         * 获取section在pre_sections中的索引
         *
         * @since 1.0
         * @param array $section 要查找的section
         * @return int pre_sections中的索引，找不到返回-1
         */
        public function get_pre_section_index( $section ) {
            foreach ( $this->pre_sections as $index => $pre_section ) {
                // 首先比较ID（如果都有ID）
                if ( isset( $section['id'] ) && isset( $pre_section['id'] ) &&
                     $section['id'] === $pre_section['id'] ) {
                    return $index;
                }

                // 如果没有ID或ID不匹配，比较标题
                if ( isset( $section['title'] ) && isset( $pre_section['title'] ) &&
                     $section['title'] === $pre_section['title'] ) {
                    // 进一步验证：如果有parent属性，也要匹配
                    $section_parent = $section['parent'] ?? '';
                    $pre_section_parent = $pre_section['parent'] ?? '';

                    if ( $section_parent === $pre_section_parent ) {
                        return $index;
                    }
                }
            }
            return -1;
        }

        /**
         * 渲染选项页面HTML
         *
         * 输出选项页面的完整HTML内容。
         *
         * @since 1.0
         */
        public function add_options_html() {

            // 使用全屏容器布局 - 响应式设计
            echo '<div class="xun-framework-page xun-fullscreen-container">';

            // 移动端遮罩层
            echo '<div id="mobile-menu-overlay" class="fixed inset-0 bg-gray-600 bg-opacity-50 lg:hidden hidden" style="z-index: 999998; top: 32px;"></div>';

            echo '<div class="flex h-full bg-gray-50">';

            // 左侧简约扁平化侧边栏 - 响应式设计
            echo '<div id="sidebar" class="fixed left-0 z-50 w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0" style="top: 32px; bottom: 0; z-index: 999999;">';

            // 侧边栏头部 - 简约设计，与右侧header对齐
            echo '<div class="px-6 py-4 relative">';

            // 移动端关闭按钮 - 移到右上角
            echo '<button id="close-sidebar" class="absolute top-2 right-2 lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 z-10">';
            echo '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
            echo '</svg>';
            echo '</button>';

            echo '<div class="flex items-center justify-center">';
            echo '<h1 class="text-lg font-semibold text-gray-900 text-center">' . esc_html( $this->args['menu_title'] ) . '</h1>';
            echo '</div>';

            // 版本号Badge - 右上角
            $version = $this->args['version'] ?? 'v1.0';
            echo '<div class="absolute top-4 right-6">';
            echo '<span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-blue-700/10 ring-inset">' . esc_html( $version ) . '</span>';
            echo '</div>';

            // 特性Badge - 居中显示
            echo '<div class="flex items-center justify-center gap-2">';

            // 简洁 - 蓝色
            echo '<span class="inline-flex items-center gap-x-1.5 rounded-md bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700">';
            echo '<svg viewBox="0 0 6 6" aria-hidden="true" class="size-1.5 fill-blue-500 xun-pulse-dot">';
            echo '<circle r="3" cx="3" cy="3" />';
            echo '</svg>';
            echo '简洁';
            echo '</span>';

            // 响应式 - 紫色
            echo '<span class="inline-flex items-center gap-x-1.5 rounded-md bg-purple-100 px-2 py-1 text-xs font-medium text-purple-700">';
            echo '<svg viewBox="0 0 6 6" aria-hidden="true" class="size-1.5 fill-purple-500 xun-pulse-dot" style="animation-delay: 0.3s;">';
            echo '<circle r="3" cx="3" cy="3" />';
            echo '</svg>';
            echo '响应式';
            echo '</span>';

            // 扁平化 - 绿色
            echo '<span class="inline-flex items-center gap-x-1.5 rounded-md bg-green-100 px-2 py-1 text-xs font-medium text-green-700">';
            echo '<svg viewBox="0 0 6 6" aria-hidden="true" class="size-1.5 fill-green-500 xun-pulse-dot" style="animation-delay: 0.6s;">';
            echo '<circle r="3" cx="3" cy="3" />';
            echo '</svg>';
            echo '扁平化';
            echo '</span>';

            echo '</div>';

            echo '</div>';
            echo '<div class="border-b border-gray-200"></div>';

            // 支持子菜单的现代化导航菜单
            echo '<nav class="flex-1 p-4 overflow-y-auto">';
            echo '<div class="space-y-1">';

            // 首先渲染欢迎页菜单项
            $this->render_welcome_menu_item();

            // 将sections按层级分组
            $grouped_sections = $this->group_sections_by_parent();

            foreach ( $grouped_sections as $group_index => $group ) {
                if ( $group['type'] === 'parent' ) {
                    // 父菜单项
                    $this->render_parent_menu_item( $group, $group_index );
                } else {
                    // 单独的菜单项（无子菜单）
                    $this->render_single_menu_item( $group, $group_index );
                }
            }

            echo '</div>'; // 结束 space-y-1
            echo '</nav>';

            // 侧边栏底部 - 版权信息
            echo '<div class="px-6 py-4 border-t border-gray-100 bg-gray-50">';
            echo '<div class="text-center text-xs text-gray-500">';

            // 获取自定义版权信息和URL
            $copyright = $this->args['copyright'] ?? '';
            $copyright_url = $this->args['copyright_url'] ?? '';

            if ( empty( $copyright ) ) {
                // 默认版权信息
                $copyright = 'Powered by XUN Framework v1.1.0';
            }

            if ( empty( $copyright_url ) ) {
                // 默认版权URL
                $copyright_url = 'https://www.xuntheme.com';
            }

            // 渲染可点击的版权链接
            echo '<a href="' . esc_url( $copyright_url ) . '" target="_blank" rel="noopener noreferrer" class="text-gray-500 hover:text-blue-600 transition-colors duration-200">';
            echo esc_html( $copyright );
            echo '</a>';
            echo '</div>';
            echo '</div>';
            echo '</div>';

            // 右侧主要内容区域 - 现代化布局，响应式设计
            echo '<div class="flex-1 flex flex-col min-h-screen lg:ml-0">';

            // 顶部标题栏 - 扁平化设计
            echo '<div class="bg-white border-b border-gray-200 px-4 sm:px-6 py-4">';
            echo '<div class="flex items-center justify-between">';

            // 左侧：移动端菜单按钮
            echo '<div class="flex items-center">';

            // 移动端菜单按钮
            echo '<button id="open-sidebar" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">';
            echo '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
            echo '</svg>';
            echo '</button>';

            echo '</div>';

            // 操作按钮 - 现代化设计，响应式布局
            echo '<div class="flex items-center space-x-2 sm:space-x-3">';

            // 导入配置按钮
            echo '<button type="button" class="inline-flex items-center px-2 sm:px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none transition-colors duration-200 xun-import">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>';
            echo '</svg>';
            echo '<span class="hidden xs:inline">导入配置</span>';
            echo '</button>';

            // 导出配置按钮
            echo '<button type="button" class="inline-flex items-center px-2 sm:px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none transition-colors duration-200 xun-export">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>';
            echo '</svg>';
            echo '<span class="hidden xs:inline">导出配置</span>';
            echo '</button>';

            // 重置当前页面按钮
            echo '<button type="button" class="inline-flex items-center px-2 sm:px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none transition-colors duration-200 xun-reset-current">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>';
            echo '</svg>';
            echo '<span class="hidden xs:inline">重置当前页</span>';
            echo '</button>';

            // 重置全部按钮
            echo '<button type="button" class="inline-flex items-center px-2 sm:px-3 py-2 border border-red-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none transition-colors duration-200 xun-reset-all">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>';
            echo '</svg>';
            echo '<span class="hidden xs:inline">重置全部</span>';
            echo '</button>';

            // 保存按钮
            echo '<button type="submit" form="xun-options-form" class="inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none transition-colors duration-200 w-auto whitespace-nowrap xun-submit">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>';
            echo '</svg>';
            echo '<span class="hidden xs:inline">保存设置</span>';
            echo '</button>';

            echo '</div>';

            // 隐藏的文件输入用于导入配置
            echo '<input type="file" id="xun-import-file" accept=".json" style="display: none;">';

            echo '</div>';
            echo '</div>';

            // 内容区域 - 简约布局，响应式设计
            echo '<div class="flex-1 p-4 sm:p-6 bg-gray-50 overflow-y-auto" style="min-height: 0;">';

            // 表单开始
            echo '<form method="post" id="xun-options-form" class="xun-form" enctype="multipart/form-data">';

            // 安全字段
            wp_nonce_field( 'xun_options_' . $this->unique, 'xun_options_nonce' );

            // 渲染欢迎页面和所有区块 - 全宽显示
            echo '<div class="w-full">';

            // 渲染欢迎页（初始隐藏，由JavaScript控制显示）
            echo '<div class="xun-section-wrapper hidden" data-section="welcome">';
            $this->render_welcome_page();
            echo '</div>';

            // 渲染所有区块（默认隐藏）
            // 直接按原始索引渲染，包括空的section（为了保持索引一致性）
            foreach ( $this->sections as $index => $section ) {
                // 只渲染有字段的section，但保持原始索引
                if ( ! empty( $section['fields'] ) ) {
                    echo '<div class="xun-section-wrapper hidden" data-section="' . $index . '">';
                    $this->render_section( $section );
                    echo '</div>';
                }
            }
            echo '</div>';

            echo '</form>';
            echo '</div>';
            echo '</div>';
            echo '</div>';

            // 只保留WordPress兼容性的必要样式
            echo '<style>
            /* WordPress管理栏高度适配 - 这些值需要根据WordPress版本动态调整 */
            .xun-fullscreen-container {
                top: 32px !important; /* WordPress管理栏高度 */
                left: 160px !important; /* WordPress侧边栏宽度 */
            }

            /* WordPress折叠侧边栏适配 */
            .folded .xun-fullscreen-container {
                left: 36px !important;
            }

            /* WordPress移动端适配 */
            @media screen and (max-width: 782px) {
                .xun-fullscreen-container {
                    top: 46px !important; /* WordPress移动端管理栏高度 */
                    left: 0 !important;
                }
            }
            </style>';
        }

        /**
         * 渲染区块
         *
         * 渲染所有配置的区块和字段。
         *
         * @since 1.0
         */
        public function render_sections() {

            if ( empty( $this->sections ) ) {
                return;
            }

            foreach ( $this->sections as $section ) {
                $this->render_section( $section );
            }
        }



        /**
         * 渲染单个区块
         *
         * 渲染指定的区块和其包含的字段。
         *
         * @since 1.0
         *
         * @param array $section 区块配置
         */
        public function render_section( $section ) {

            // 使用和欢迎页一样的card容器卡片样式
            echo '<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">';

            // 区块头部 - 放在card内部
            if ( ! empty( $section['title'] ) || ! empty( $section['desc'] ) ) {
                echo '<div class="px-8 border-b border-gray-200 bg-gray-50 rounded-t-lg">';

                if ( ! empty( $section['title'] ) ) {
                    $icon = $section['icon'] ?? 'dashicons-admin-generic';
                    echo '<div class="flex items-center mb-2">';
                    echo '<span class="dashicons ' . esc_attr( $icon ) . ' mr-3 text-xl text-blue-600"></span>';
                    echo '<h2 class="text-xl font-semibold text-gray-900">' . esc_html( $section['title'] ) . '</h2>';
                    echo '</div>';
                }

                if ( ! empty( $section['desc'] ) ) {
                    echo '<p class="text-gray-600 ml-9">' . wp_kses_post( $section['desc'] ) . '</p>';
                }

                echo '</div>';
            }

            // 字段内容区域 - 放在card内部，添加padding和底部圆角
            echo '<div class="p-8 rounded-b-lg">';
            if ( ! empty( $section['fields'] ) ) {
                echo '<div class="space-y-6">';
                foreach ( $section['fields'] as $field ) {
                    $this->render_field( $field );
                }
                echo '</div>';
            } else {
                echo '<div class="text-center py-12 text-gray-500">';
                echo '<svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />';
                echo '</svg>';
                echo '<p class="text-lg">暂无配置字段</p>';
                echo '</div>';
            }
            echo '</div>';

            echo '</div>'; // 结束card容器
        }

        /**
         * 渲染单个字段
         *
         * 使用统一的字段工厂方法渲染指定的字段。
         *
         * @since 1.0
         *
         * @param array $field 字段配置
         */
        public function render_field( $field ) {

            if ( empty( $field['type'] ) || empty( $field['id'] ) ) {
                return;
            }

            // 获取字段值
            $field_value = $this->get_field_value( $field['id'], $this->options, $this->get_default( $field ) );

            // 现代化字段容器
            echo '<div class="space-y-2" data-field-id="' . esc_attr( $field['id'] ) . '"';
            if ( ! empty( $field['required'] ) ) {
                echo ' data-required="true"';
            }
            echo '>';

            // 使用统一的字段工厂方法
            XUN::field( $field, $field_value, $this->unique, 'options' );

            echo '</div>';
        }

        /**
         * 渲染欢迎页面
         *
         * @since 1.0
         */
        public function render_welcome_page() {
            // 获取自定义欢迎页面内容
            $welcome_content = $this->args['welcome_content'] ?? '';

            if ( ! empty( $welcome_content ) ) {
                // 显示自定义欢迎内容
                echo '<div class="bg-white rounded-lg shadow-sm border border-gray-200">';
                echo wp_kses_post($welcome_content);
                echo '</div>';
            } else {
                // 显示默认欢迎页面
                $this->render_default_welcome_page();
            }
        }

        /**
         * 渲染默认欢迎页面
         *
         * @since 1.0
         */
        public function render_default_welcome_page() {
            echo '<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">';

            // 欢迎页面头部
            echo '<div class="bg-gradient-to-r from-blue-500 to-blue-600 px-8 py-12 text-white text-center">';
            echo '<div class="max-w-2xl mx-auto">';
            echo '<div class="mb-6">';
            echo '<span class="inline-block p-4 bg-white bg-opacity-20 rounded-full">';
            echo '<span class="dashicons dashicons-admin-settings text-4xl"></span>';
            echo '</span>';
            echo '</div>';
            echo '<h1 class="text-3xl font-bold mb-4">欢迎使用 Xun Framework </h1>';
            echo '<p class="text-blue-100 text-lg">现代化的WordPress选项框架，让您的主题配置更加简单高效</p>';
            echo '</div>';
            echo '</div>';

            // 欢迎页面内容
            echo '<div class="p-8">';

            // 功能特色
            echo '<div class="grid md:grid-cols-3 gap-6 mb-8">';

            // 特色1
            echo '<div class="text-center p-6 bg-gray-50 rounded-lg">';
            echo '<div class="mb-4">';
            echo '<span class="dashicons dashicons-art text-3xl text-blue-500"></span>';
            echo '</div>';
            echo '<h3 class="text-lg font-semibold mb-2 text-gray-900">现代化设计</h3>';
            echo '<p class="text-gray-600 text-sm">基于TailwindCSS 4.x构建，提供简约扁平化的用户界面</p>';
            echo '</div>';

            // 特色2
            echo '<div class="text-center p-6 bg-gray-50 rounded-lg">';
            echo '<div class="mb-4">';
            echo '<span class="dashicons dashicons-performance text-3xl text-green-500"></span>';
            echo '</div>';
            echo '<h3 class="text-lg font-semibold mb-2 text-gray-900">高性能</h3>';
            echo '<p class="text-gray-600 text-sm">轻量级框架设计，快速响应，不影响网站性能</p>';
            echo '</div>';

            // 特色3
            echo '<div class="text-center p-6 bg-gray-50 rounded-lg">';
            echo '<div class="mb-4">';
            echo '<span class="dashicons dashicons-admin-tools text-3xl text-purple-500"></span>';
            echo '</div>';
            echo '<h3 class="text-lg font-semibold mb-2 text-gray-900">易于使用</h3>';
            echo '<p class="text-gray-600 text-sm">直观的配置界面，支持多种字段类型和验证规则</p>';
            echo '</div>';

            echo '</div>';

            // 快速开始
            echo '<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">';
            echo '<h3 class="text-lg font-semibold mb-3 text-blue-900 flex items-center">';
            echo '<span class="dashicons dashicons-lightbulb mr-2"></span>';
            echo '快速开始';
            echo '</h3>';
            echo '<div class="text-blue-800 space-y-2">';
            echo '<p>• 点击左侧菜单项开始配置您的主题选项</p>';
            echo '<p>• 所有设置都会自动保存到WordPress数据库</p>';
            echo '<p>• 支持实时预览和重置功能</p>';
            echo '</div>';
            echo '</div>';

            // 版本信息
            echo '<div class="mt-8 pt-6 border-t border-gray-200 text-center text-gray-500 text-sm">';
            echo '<p>Xun Framework v1.0 | 基于 TailwindCSS 4.x 构建</p>';
            echo '</div>';

            echo '</div>';
            echo '</div>';
        }

        /**
         * 渲染欢迎页菜单项
         *
         * @since 1.0
         */
        public function render_welcome_menu_item() {
            $is_active = true; // 默认激活欢迎页

            echo '<div class="xun-nav-item relative" data-section="welcome" data-slug="welcome">';

            // 左侧指示条
            echo '<div class="xun-nav-indicator absolute left-0 top-0 bottom-0 w-1 rounded-r transition-all duration-300 ' . ($is_active ? 'opacity-100' : 'opacity-0') . '"></div>';

            // 菜单内容
            $content_classes = $is_active
                ? 'bg-blue-50 text-blue-700 border border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 border border-transparent';
            echo '<div class="xun-nav-content pl-4 pr-3 py-3 rounded-lg cursor-pointer transition-all duration-200 ' . $content_classes . '">';

            // 图标和标题
            echo '<div class="flex items-center">';
            echo '<span class="dashicons dashicons-welcome-learn-more mr-3 flex-shrink-0 text-base"></span>';
            echo '<div class="flex-1 min-w-0">';
            echo '<span class="font-medium text-sm truncate block">欢迎页</span>';
            $desc_color = $is_active ? 'text-blue-600' : 'text-gray-500';
            echo '<div class="xun-nav-desc text-xs ' . $desc_color . ' mt-1 truncate">框架介绍和快速开始</div>';
            echo '</div>';
            echo '</div>';

            echo '</div>'; // 结束 nav-content
            echo '</div>'; // 结束 nav-item
        }

        /**
         * 将sections按层级分组
         *
         * 分析sections数组，将其按父子关系分组
         *
         * @since 1.0
         * @return array 分组后的sections
         */
        public function group_sections_by_parent() {
            $grouped = array();

            // 简化逻辑：按原始顺序处理所有sections，保持索引不变
            foreach ( $this->sections as $index => $section ) {
                // 跳过有parent属性的section（子菜单项），它们会在父菜单处理时被包含
                if ( isset( $section['parent'] ) && ! empty( $section['parent'] ) ) {
                    continue;
                }

                $section_id = $section['id'] ?? 'section_' . $index;
                $children = array();

                // 查找这个section的所有子菜单项
                foreach ( $this->sections as $child_index => $child_section ) {
                    if ( isset( $child_section['parent'] ) && $child_section['parent'] === $section_id ) {
                        $children[] = array(
                            'section' => $child_section,
                            'index' => $child_index
                        );
                    }
                }

                if ( ! empty( $children ) ) {
                    // 这是一个有子菜单的父菜单项
                    $grouped[] = array(
                        'type' => 'parent',
                        'id' => $section_id,
                        'section' => $section,
                        'index' => $index, // 始终使用原始索引
                        'children' => $children,
                        'expanded' => false
                    );
                } else {
                    // 这是一个独立的菜单项
                    $grouped[] = array(
                        'type' => 'single',
                        'section' => $section,
                        'index' => $index
                    );
                }
            }

            // 处理孤立的子菜单项（找不到父菜单的）
            foreach ( $this->sections as $index => $section ) {
                if ( isset( $section['parent'] ) && ! empty( $section['parent'] ) ) {
                    $parent_id = $section['parent'];
                    $parent_found = false;

                    // 检查是否已经被包含在某个父菜单中
                    foreach ( $grouped as $group ) {
                        if ( $group['type'] === 'parent' && $group['id'] === $parent_id ) {
                            $parent_found = true;
                            break;
                        }
                    }

                    // 如果没有找到父菜单，将其作为独立菜单项
                    if ( ! $parent_found ) {
                        $grouped[] = array(
                            'type' => 'single',
                            'section' => $section,
                            'index' => $index
                        );
                    }
                }
            }

            return $grouped;
        }

        /**
         * 获取第一个有效的section索引（排除父菜单项）
         * 注意：现在默认显示欢迎页面，所以这个方法返回-1表示不激活任何菜单项
         *
         * @since 1.0
         * @return int 第一个有效的section索引，-1表示显示欢迎页面
         */
        public function get_first_valid_section_index() {
            // 默认显示欢迎页面，不激活任何菜单项
            return -1;
        }

        /**
         * 生成URL slug
         *
         * @since 1.0
         * @param string $text 文本
         * @return string slug
         */
        public function generate_slug( $text ) {
            // 简化处理：直接使用原文本，只替换空格为连字符
            $slug = str_replace( ' ', '-', $text );
            // 移除其他特殊字符，保留中文、英文、数字和连字符
            $slug = preg_replace( '/[^\w\-\x{4e00}-\x{9fa5}]/u', '', $slug );
            // 移除多余的连字符
            $slug = preg_replace( '/-+/', '-', $slug );
            // 移除首尾连字符
            $slug = trim( $slug, '-' );

            // 如果为空，使用时间戳作为后备
            if ( empty( $slug ) ) {
                $slug = 'section-' . time();
            }

            return $slug;
        }

        /**
         * 渲染父菜单项（带子菜单）
         *
         * @since 1.0
         * @param array $group 菜单组数据
         * @param int $group_index 组索引
         */
        public function render_parent_menu_item( $group, $group_index ) {
            $section = $group['section'];
            $is_expanded = $group['expanded'];
            $icon = $section['icon'] ?? 'dashicons-admin-generic';
            $title = $section['title'] ?? '菜单组 ' . ($group_index + 1);
            $section_index = $group['index'];
            $has_fields = ! empty( $section['fields'] );

            echo '<div class="xun-nav-group" data-group="' . $group_index . '">';

            // 父菜单项 - 如果有字段则设置data-section和相关属性
            if ( $has_fields ) {
                $slug = $this->generate_slug( $title );
                echo '<div class="xun-nav-parent xun-nav-item relative cursor-pointer" data-section="' . $section_index . '" data-slug="' . esc_attr( $slug ) . '">';
            } else {
                echo '<div class="xun-nav-parent relative cursor-pointer">';
            }

            // 左侧指示条（有字段的父菜单可以显示激活状态）
            if ( $has_fields ) {
                echo '<div class="xun-nav-indicator absolute left-0 top-0 bottom-0 w-1 rounded-r transition-all duration-300 opacity-0"></div>';
            } else {
                echo '<div class="absolute left-0 top-0 bottom-0 w-1 rounded-r transition-all duration-300 opacity-0"></div>';
            }

            // 父菜单内容
            if ( $has_fields ) {
                echo '<div class="xun-nav-content pl-4 pr-3 py-3 rounded-lg transition-all duration-200 text-gray-700 hover:bg-gray-50 border border-transparent">';
            } else {
                echo '<div class="pl-4 pr-3 py-3 rounded-lg transition-all duration-200 text-gray-700 hover:bg-gray-50 border border-transparent">';
            }

            // 图标、标题和展开箭头（支持描述时的垂直居中）
            $desc = $section['desc'] ?? '';
            if ( ! empty( $desc ) ) {
                // 有描述时，使用flex居中对齐
                echo '<div class="flex items-center justify-between">';
                echo '<div class="flex items-center">';
                echo '<span class="dashicons ' . esc_attr( $icon ) . ' mr-3 flex-shrink-0 text-base"></span>';
                echo '<div class="flex-1 min-w-0">';
                echo '<span class="font-medium text-sm truncate block">' . esc_html( $title ) . '</span>';
                $desc_color = 'text-gray-500';
                echo '<div class="text-xs ' . $desc_color . ' mt-1 truncate">' . esc_html( $desc ) . '</div>';
                echo '</div>';
                echo '</div>';
            } else {
                // 无描述时，简单对齐
                echo '<div class="flex items-center justify-between">';
                echo '<div class="flex items-center">';
                echo '<span class="dashicons ' . esc_attr( $icon ) . ' mr-3 flex-shrink-0 text-base"></span>';
                echo '<span class="font-medium text-sm truncate">' . esc_html( $title ) . '</span>';
                echo '</div>';
            }

            // 展开/收起箭头 - 默认未旋转（折叠状态）
            echo '<svg class="w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
            echo '</svg>';
            echo '</div>';

            echo '</div>'; // 结束 nav-content
            echo '</div>'; // 结束 nav-parent

            // 子菜单容器 - 默认隐藏
            echo '<div class="xun-nav-children ml-4 mt-1 space-y-1 hidden">';

            foreach ( $group['children'] as $child ) {
                $this->render_child_menu_item( $child, $group_index );
            }

            echo '</div>'; // 结束 nav-children
            echo '</div>'; // 结束 nav-group
        }

        /**
         * 渲染子菜单项
         *
         * @since 1.0
         * @param array $child 子菜单数据
         * @param int $group_index 父组索引
         */
        public function render_child_menu_item( $child, $group_index ) {
            $section = $child['section'];
            $index = $child['index'];
            $first_valid_index = $this->get_first_valid_section_index();
            $is_active = false; // 默认不激活，因为欢迎页是默认激活的
            $icon = $section['icon'] ?? '📄';
            $title = $section['title'] ?? '子项 ' . ($index + 1);
            $desc = $section['desc'] ?? '';

            $slug = $this->generate_slug( $title );
            echo '<div class="xun-nav-item xun-nav-child relative" data-section="' . $index . '" data-slug="' . esc_attr( $slug ) . '">';

            // 左侧指示条
            echo '<div class="xun-nav-indicator absolute left-0 top-0 bottom-0 w-1 rounded-r transition-all duration-300 ' . ($is_active ? 'opacity-100' : 'opacity-0') . '"></div>';

            // 子菜单内容
            $content_classes = $is_active
                ? 'bg-blue-50 text-blue-700 border border-blue-200'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-700 border border-transparent';
            echo '<div class="xun-nav-content pl-6 pr-3 py-2 rounded-lg cursor-pointer transition-all duration-200 ' . $content_classes . '">';

            // 图标和标题（支持描述时的垂直居中）
            $desc = $section['desc'] ?? '';
            if ( ! empty( $desc ) ) {
                // 有描述时，使用flex居中对齐
                echo '<div class="flex items-center">';
                echo '<span class="dashicons ' . esc_attr( $icon ) . ' mr-2 flex-shrink-0 text-sm"></span>';
                echo '<div class="flex-1 min-w-0">';
                echo '<span class="font-medium text-sm truncate block">' . esc_html( $title ) . '</span>';
                $desc_color = $is_active ? 'text-blue-600' : 'text-gray-500';
                echo '<div class="xun-nav-desc text-xs ' . $desc_color . ' mt-1 truncate">' . esc_html( $desc ) . '</div>';
                echo '</div>';
                echo '</div>';
            } else {
                // 无描述时，简单对齐
                echo '<div class="flex items-center">';
                echo '<span class="dashicons ' . esc_attr( $icon ) . ' mr-2 flex-shrink-0 text-sm"></span>';
                echo '<span class="font-medium text-sm truncate">' . esc_html( $title ) . '</span>';
                echo '</div>';
            }

            echo '</div>'; // 结束 nav-content
            echo '</div>'; // 结束 nav-item
        }

        /**
         * 渲染单独菜单项（无子菜单）
         *
         * @since 1.0
         * @param array $group 菜单数据
         * @param int $group_index 组索引
         */
        public function render_single_menu_item( $group, $group_index ) {
            $section = $group['section'];
            $index = $group['index'];
            $first_valid_index = $this->get_first_valid_section_index();
            $is_active = false; // 默认不激活，因为欢迎页是默认激活的
            $icon = $section['icon'] ?? 'dashicons-admin-generic';
            $title = $section['title'] ?? '区块 ' . ($index + 1);
            $desc = $section['desc'] ?? '';

            $slug = $this->generate_slug( $title );
            echo '<div class="xun-nav-item relative" data-section="' . $index . '" data-slug="' . esc_attr( $slug ) . '">';

            // 左侧指示条
            echo '<div class="xun-nav-indicator absolute left-0 top-0 bottom-0 w-1 rounded-r transition-all duration-300 ' . ($is_active ? 'opacity-100' : 'opacity-0') . '"></div>';

            // 菜单内容
            $content_classes = $is_active
                ? 'bg-blue-50 text-blue-700 border border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 border border-transparent';
            echo '<div class="xun-nav-content pl-4 pr-3 py-3 rounded-lg cursor-pointer transition-all duration-200 ' . $content_classes . '">';

            // 图标和标题（支持描述时的垂直居中）
            if ( ! empty( $desc ) ) {
                // 有描述时，使用flex居中对齐
                echo '<div class="flex items-center">';
                echo '<span class="dashicons ' . esc_attr( $icon ) . ' mr-3 flex-shrink-0 text-base"></span>';
                echo '<div class="flex-1 min-w-0">';
                echo '<span class="font-medium text-sm truncate block">' . esc_html( $title ) . '</span>';
                $desc_color = $is_active ? 'text-blue-600' : 'text-gray-500';
                echo '<div class="xun-nav-desc text-xs ' . $desc_color . ' mt-1 truncate">' . esc_html( $desc ) . '</div>';
                echo '</div>';
                echo '</div>';
            } else {
                // 无描述时，简单对齐
                echo '<div class="flex items-center">';
                echo '<span class="dashicons ' . esc_attr( $icon ) . ' mr-3 flex-shrink-0 text-base"></span>';
                echo '<span class="font-medium text-sm truncate">' . esc_html( $title ) . '</span>';
                echo '</div>';
            }

            echo '</div>'; // 结束 nav-content
            echo '</div>'; // 结束 nav-item
        }

        /**
         * AJAX导出配置
         */
        public function ajax_export() {
            // 验证权限
            if ( ! current_user_can( 'manage_options' ) ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '权限不足' ) ) );
            }

            // 获取当前配置
            $options = get_option( $this->unique, array() );

            // 添加导出信息
            $export_data = array(
                'xun_framework_export' => true,
                'export_time' => current_time( 'mysql' ),
                'site_url' => home_url(),
                'framework_version' => '1.0',
                'options' => $options
            );

            wp_die( json_encode( array(
                'success' => true,
                'data' => $export_data,
                'filename' => 'xun-options-' . date( 'Y-m-d-H-i-s' ) . '.json'
            ) ) );
        }

        /**
         * AJAX导入配置
         */
        public function ajax_import() {
            // 验证权限
            if ( ! current_user_can( 'manage_options' ) ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '权限不足' ) ) );
            }

            // 检查是否有上传的文件
            if ( ! isset( $_POST['import_data'] ) ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '没有找到导入数据' ) ) );
            }

            // 解析JSON数据
            $import_data = json_decode( stripslashes( $_POST['import_data'] ), true );

            if ( json_last_error() !== JSON_ERROR_NONE ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '配置文件格式错误' ) ) );
            }

            // 验证是否是有效的导出文件
            if ( ! isset( $import_data['xun_framework_export'] ) || ! $import_data['xun_framework_export'] ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '不是有效的配置文件' ) ) );
            }

            // 导入配置
            if ( isset( $import_data['options'] ) ) {
                $saved = update_option( $this->unique, $import_data['options'] );

                // 验证导入是否成功
                $verify_options = get_option( $this->unique, array() );
                $import_successful = ( $verify_options === $import_data['options'] );

                if ( $import_successful ) {
                    wp_die( json_encode( array( 'success' => true, 'message' => '配置导入成功' ) ) );
                } else {
                    wp_die( json_encode( array( 'success' => false, 'message' => '配置导入失败' ) ) );
                }
            } else {
                wp_die( json_encode( array( 'success' => false, 'message' => '配置文件中没有找到选项数据' ) ) );
            }
        }

        /**
         * AJAX重置当前页面
         */
        public function ajax_reset_current() {
            // 验证nonce
            if ( ! wp_verify_nonce( $_POST['_wpnonce'] ?? '', 'xun_options_nonce' ) ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '安全验证失败' ) ) );
            }

            // 验证权限
            if ( ! current_user_can( 'manage_options' ) ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '权限不足' ) ) );
            }

            $section_index = intval( $_POST['section_index'] ?? 0 );

            if ( ! isset( $this->sections[ $section_index ] ) ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '页面不存在' ) ) );
            }

            $section = $this->sections[ $section_index ];
            $current_options = get_option( $this->unique, array() );

            // 重置当前页面的字段为默认值
            if ( isset( $section['fields'] ) ) {
                foreach ( $section['fields'] as $field ) {
                    if ( isset( $field['id'] ) ) {
                        $field_id = $field['id'];

                        if ( isset( $field['default'] ) ) {
                            $current_options[ $field_id ] = $field['default'];
                        } else {
                            unset( $current_options[ $field_id ] );
                        }
                    }
                }
            }

            // 强制更新选项，即使值相同
            delete_option( $this->unique );
            $saved = add_option( $this->unique, $current_options );

            // 验证数据是否正确保存
            $verify_options = get_option( $this->unique, array() );

            if ( $verify_options === $current_options ) {
                wp_die( json_encode( array( 'success' => true, 'message' => '当前页面已重置' ) ) );
            } else {
                wp_die( json_encode( array( 'success' => false, 'message' => '重置失败' ) ) );
            }
        }

        /**
         * AJAX重置全部配置
         */
        public function ajax_reset_all() {
            // 验证nonce
            if ( ! wp_verify_nonce( $_POST['_wpnonce'] ?? '', 'xun_options_nonce' ) ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '安全验证失败' ) ) );
            }

            // 验证权限
            if ( ! current_user_can( 'manage_options' ) ) {
                wp_die( json_encode( array( 'success' => false, 'message' => '权限不足' ) ) );
            }

            // 删除所有配置
            $deleted = delete_option( $this->unique );

            if ( $deleted ) {
                wp_die( json_encode( array( 'success' => true, 'message' => '所有配置已重置' ) ) );
            } else {
                wp_die( json_encode( array( 'success' => false, 'message' => '重置失败' ) ) );
            }
        }


    }
}
