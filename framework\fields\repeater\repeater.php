<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 重复器字段类型
 * 
 * 这个类实现了现代化的重复器字段功能。
 * 提供比CSF更好的用户体验，包括拖拽排序、折叠展开、批量操作等高级功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_repeater' ) ) {
    
    /**
     * XUN_Field_repeater 重复器字段类 - 响应式自适应设计
     *
     * 提供现代化重复器字段的完整功能，包括：
     * - 拖拽排序
     * - 折叠展开
     * - 批量操作
     * - 实时预览
     * - 键盘快捷键
     * - 无障碍访问
     * - 自动保存
     * - 数据验证
     * - 完全响应式设计（移动端优先）
     * - 触摸友好的交互体验
     * - 自适应布局和间距
     * - 跨设备兼容性优化
     *
     * @since 1.0
     */
    class XUN_Field_repeater extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化重复器字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染重复器字段
         *
         * 输出现代化重复器字段的HTML代码。
         *
         * @since 1.0
         */
        public function render() {

            // 检查字段ID冲突
            if ( preg_match( '/'. preg_quote( '['. $this->field['id'] .']' ) .'/', $this->unique ) ) {
                echo '<div class="bg-red-50 border border-red-200 rounded-md p-4 mb-4">';
                echo '<div class="flex">';
                echo '<div class="flex-shrink-0">';
                echo '<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">';
                echo '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>';
                echo '</svg>';
                echo '</div>';
                echo '<div class="ml-3">';
                echo '<h3 class="text-sm font-medium text-red-800">字段ID冲突错误</h3>';
                echo '<div class="mt-2 text-sm text-red-700">';
                echo '<p>重复器字段ID "' . esc_html( $this->field['id'] ) . '" 与父级字段冲突，请使用不同的字段ID。</p>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                return;
            }

            // 获取字段配置
            $fields = ! empty( $this->field['fields'] ) ? $this->field['fields'] : array();
            $max = ! empty( $this->field['max'] ) ? intval( $this->field['max'] ) : 0;
            $min = ! empty( $this->field['min'] ) ? intval( $this->field['min'] ) : 0;
            $button_title = ! empty( $this->field['button_title'] ) ? $this->field['button_title'] : '添加项目';
            $sortable = ! empty( $this->field['sortable'] ) ? $this->field['sortable'] : true;
            $collapsible = ! empty( $this->field['collapsible'] ) ? $this->field['collapsible'] : true;
            $show_preview = ! empty( $this->field['show_preview'] ) ? $this->field['show_preview'] : true;
            $preview_field = ! empty( $this->field['preview_field'] ) ? $this->field['preview_field'] : '';
            $layout = ! empty( $this->field['layout'] ) ? $this->field['layout'] : 'default';
            $color = ! empty( $this->field['color'] ) ? $this->field['color'] : 'blue';

            // 输出前置内容
            echo $this->field_before();

            // 开始重复器容器 - 响应式自适应设计
            echo '<div class="xun-repeater-field w-full touch-manipulation" data-field-id="' . esc_attr( $this->field['id'] ) . '" data-max="' . esc_attr( $max ) . '" data-min="' . esc_attr( $min ) . '" data-sortable="' . esc_attr( $sortable ? 'true' : 'false' ) . '" data-collapsible="' . esc_attr( $collapsible ? 'true' : 'false' ) . '" data-layout="' . esc_attr( $layout ) . '" data-color="' . esc_attr( $color ) . '" data-preview-field="' . esc_attr( $preview_field ) . '">';

            // 添加响应式CSS样式
            echo '<style>
                @media (max-width: 640px) {
                    .xun-repeater-field .xun-repeater-toolbar {
                        flex-direction: column !important;
                        gap: 0.75rem !important;
                        padding: 1rem !important;
                    }
                    .xun-repeater-field .xun-repeater-item {
                        padding: 1rem !important;
                        margin-bottom: 1rem !important;
                    }
                    .xun-repeater-field .xun-repeater-item-header {
                        flex-direction: column !important;
                        align-items: flex-start !important;
                        gap: 0.75rem !important;
                    }
                    .xun-repeater-field .xun-repeater-item-actions {
                        width: 100% !important;
                        justify-content: space-between !important;
                    }
                    .xun-repeater-field .xun-repeater-add-button {
                        width: 100% !important;
                        padding: 1rem !important;
                        font-size: 1rem !important;
                    }
                    .xun-repeater-field .xun-repeater-items {
                        gap: 1rem !important;
                    }
                }
                @media (min-width: 640px) and (max-width: 767px) {
                    .xun-repeater-field .xun-repeater-toolbar {
                        padding: 0.875rem !important;
                    }
                    .xun-repeater-field .xun-repeater-item {
                        padding: 0.875rem !important;
                    }
                    .xun-repeater-field .xun-repeater-add-button {
                        padding: 0.75rem 1.5rem !important;
                    }
                }
                @media (min-width: 768px) {
                    .xun-repeater-field .xun-repeater-toolbar {
                        padding: 0.75rem !important;
                    }
                    /* 确保桌面端显示完整文本 */
                    .xun-repeater-field .xun-repeater-full-text {
                        display: inline-block !important;
                    }
                    .xun-repeater-field .xun-repeater-short-text {
                        display: none !important;
                    }
                }
                @media (max-width: 767px) {
                    /* 确保移动端显示简化文本 */
                    .xun-repeater-field .xun-repeater-full-text {
                        display: none !important;
                    }
                    .xun-repeater-field .xun-repeater-short-text {
                        display: inline-block !important;
                    }
                }
            </style>';

            // 工具栏
            $this->render_toolbar( $max, $min, $sortable, $collapsible );

            // 重复器项目容器 - 响应式间距
            echo '<div class="xun-repeater-items space-y-6 sm:space-y-4 md:space-y-4" data-sortable-container>';

            // 渲染现有项目
            if ( ! empty( $this->value ) && is_array( $this->value ) ) {
                foreach ( $this->value as $index => $item_value ) {
                    $this->render_item( $fields, $item_value, $index, $collapsible, $show_preview, $preview_field, $color );
                }
            }

            echo '</div>'; // 结束项目容器

            // 空状态提示
            $this->render_empty_state();

            // 添加按钮
            $this->render_add_button( $button_title, $max, $color );

            // 限制提示
            $this->render_limit_alerts( $max, $min );

            // 隐藏的模板项目
            $this->render_template_item( $fields, $collapsible, $show_preview, $preview_field, $color );

            echo '</div>'; // 结束重复器容器

            // 输出后置内容
            echo $this->field_after();
        }

        /**
         * 渲染工具栏
         * 
         * @since 1.0
         */
        private function render_toolbar( $max, $min, $sortable, $collapsible ) {
            echo '<div class="xun-repeater-toolbar flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 sm:mb-4 md:mb-4 p-4 sm:p-3 md:p-3 bg-gray-50 rounded-lg border border-gray-200 gap-4 sm:gap-0">';

            // 左侧信息 - 响应式设计
            echo '<div class="flex items-center space-x-4 w-full sm:w-auto">';
            echo '<div class="text-base sm:text-sm md:text-sm text-gray-600 font-medium sm:font-normal">';
            echo '<span class="xun-repeater-count">0</span> 个项目';
            if ( $max > 0 ) {
                echo ' / 最多 ' . $max . ' 个';
            }
            if ( $min > 0 ) {
                echo ' / 最少 ' . $min . ' 个';
            }
            echo '</div>';
            echo '</div>';

            // 右侧操作 - 响应式设计
            echo '<div class="flex flex-wrap items-center gap-2 w-full sm:w-auto justify-start sm:justify-end">';
            
            if ( $collapsible ) {
                echo '<button type="button" class="xun-repeater-collapse-all inline-flex items-center px-4 py-2 sm:px-3 sm:py-1.5 md:px-3 md:py-1.5 border border-gray-300 shadow-sm text-sm sm:text-xs md:text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150 touch-manipulation">';
                echo '<svg class="w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4 mr-2 sm:mr-1 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>';
                echo '</svg>';
                echo '<span class="xun-repeater-full-text">全部折叠</span><span class="xun-repeater-short-text">折叠</span>';
                echo '</button>';

                echo '<button type="button" class="xun-repeater-expand-all inline-flex items-center px-4 py-2 sm:px-3 sm:py-1.5 md:px-3 md:py-1.5 border border-gray-300 shadow-sm text-sm sm:text-xs md:text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150 touch-manipulation">';
                echo '<svg class="w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4 mr-2 sm:mr-1 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>';
                echo '</svg>';
                echo '<span class="xun-repeater-full-text">全部展开</span><span class="xun-repeater-short-text">展开</span>';
                echo '</button>';
            }

            echo '<button type="button" class="xun-repeater-clear-all inline-flex items-center px-4 py-2 sm:px-3 sm:py-1.5 md:px-3 md:py-1.5 border border-red-300 shadow-sm text-sm sm:text-xs md:text-xs font-medium rounded text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150 touch-manipulation">';
            echo '<svg class="w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4 mr-2 sm:mr-1 md:mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>';
            echo '</svg>';
            echo '<span class="xun-repeater-full-text">清空全部</span><span class="xun-repeater-short-text">清空</span>';
            echo '</button>';

            echo '</div>';
            echo '</div>';
        }

        /**
         * 渲染单个项目
         * 
         * @since 1.0
         */
        private function render_item( $fields, $item_value, $index, $collapsible, $show_preview, $preview_field, $color ) {
            echo '<div class="xun-repeater-item bg-white border border-gray-200 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md touch-manipulation" data-index="' . esc_attr( $index ) . '">';

            // 项目头部
            $this->render_item_header( $item_value, $index, $collapsible, $show_preview, $preview_field, $color );

            // 项目内容 - 响应式内边距
            echo '<div class="xun-repeater-item-content p-6 sm:p-4 md:p-4 space-y-6 sm:space-y-4 md:space-y-4" aria-hidden="false">';
            
            foreach ( $fields as $field ) {
                $field_unique = ( ! empty( $this->unique ) ) ? $this->unique .'['. $this->field['id'] .']['. $index .']' : $this->field['id'] .'['. $index .']';
                $field_value = ( isset( $field['id'] ) && isset( $item_value[$field['id']] ) ) ? $item_value[$field['id']] : '';
                
                // 使用XUN框架的字段渲染方法
                XUN::field( $field, $field_value, $field_unique, 'field/repeater' );
            }
            
            echo '</div>'; // 结束项目内容
            echo '</div>'; // 结束项目
        }

        /**
         * 渲染项目头部
         * 
         * @since 1.0
         */
        private function render_item_header( $item_value, $index, $collapsible, $show_preview, $preview_field, $color ) {
            echo '<div class="xun-repeater-item-header flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 sm:p-3 md:p-3 bg-gray-50 border-b border-gray-200 rounded-t-lg cursor-pointer gap-3 sm:gap-0" role="button" tabindex="0" aria-expanded="true" aria-label="展开或折叠此项目">';

            // 左侧：拖拽手柄和标题 - 响应式布局
            echo '<div class="flex items-center space-x-3 w-full sm:w-auto">';

            // 拖拽手柄 - 响应式尺寸
            echo '<div class="xun-repeater-sort-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors touch-manipulation">';
            echo '<svg class="w-6 h-6 sm:w-5 sm:h-5 md:w-5 md:h-5" fill="currentColor" viewBox="0 0 20 20">';
            echo '<path d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zM8 4h4v2H8V4zm0 4h4v2H8V8zm0 4h4v2H8v-2z"></path>';
            echo '</svg>';
            echo '</div>';

            // 项目标题和预览 - 响应式设计
            echo '<div class="flex-1 min-w-0">';
            echo '<div class="flex items-center space-x-2">';

            // 动态标题：如果有preview_field且有值，显示该值作为标题，否则显示"项目 #*"
            $title_text = '项目 #' . ($index + 1);
            if ( $preview_field && isset( $item_value[$preview_field] ) ) {
                $preview_value = $item_value[$preview_field];
                if ( ! empty( $preview_value ) ) {
                    $title_text = esc_html( wp_trim_words( $preview_value, 8 ) );
                }
            }

            echo '<span class="xun-repeater-item-title text-base sm:text-sm md:text-sm font-medium text-gray-900 truncate" data-default-title="项目 #' . ($index + 1) . '">' . $title_text . '</span>';
            echo '</div>';
            echo '</div>';

            echo '</div>';

            // 右侧：操作按钮 - 响应式设计
            echo '<div class="xun-repeater-item-actions flex items-center space-x-2 sm:space-x-1 md:space-x-1 w-full sm:w-auto justify-end">';
            
            if ( $collapsible ) {
                echo '<button type="button" class="xun-repeater-toggle inline-flex items-center p-2 sm:p-1.5 md:p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors touch-manipulation" title="折叠/展开" aria-expanded="true" aria-label="展开或折叠">';
                echo '<svg class="w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>';
                echo '</svg>';
                echo '</button>';
            }

            echo '<button type="button" class="xun-repeater-clone inline-flex items-center p-2 sm:p-1.5 md:p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors touch-manipulation" title="复制项目">';
            echo '<svg class="w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>';
            echo '</svg>';
            echo '</button>';

            echo '<button type="button" class="xun-repeater-remove inline-flex items-center p-2 sm:p-1.5 md:p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors touch-manipulation" title="删除项目">';
            echo '<svg class="w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>';
            echo '</svg>';
            echo '</button>';
            
            echo '</div>';
            echo '</div>';
        }

        /**
         * 渲染空状态
         * 
         * @since 1.0
         */
        private function render_empty_state() {
            echo '<div class="xun-repeater-empty-state hidden text-center py-16 sm:py-12 md:py-12 px-6 sm:px-4 md:px-4">';
            echo '<div class="mx-auto w-28 h-28 sm:w-24 sm:h-24 md:w-24 md:h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6 sm:mb-4 md:mb-4">';
            echo '<svg class="w-14 h-14 sm:w-12 sm:h-12 md:w-12 md:h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
            echo '</svg>';
            echo '</div>';
            echo '<h3 class="text-xl sm:text-lg md:text-lg font-medium text-gray-900 mb-3 sm:mb-2 md:mb-2">暂无项目</h3>';
            echo '<p class="text-base sm:text-sm md:text-sm text-gray-500 mb-8 sm:mb-6 md:mb-6">点击下方按钮添加第一个项目</p>';
            echo '</div>';
        }

        /**
         * 渲染添加按钮
         * 
         * @since 1.0
         */
        private function render_add_button( $button_title, $max, $color ) {
            echo '<div class="xun-repeater-add-container mt-6 sm:mt-4 md:mt-4">';
            echo '<button type="button" class="xun-repeater-add w-full inline-flex items-center justify-center px-6 py-4 sm:px-4 sm:py-3 md:px-4 md:py-3 border border-dashed border-gray-300 rounded-lg text-base sm:text-sm md:text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-' . esc_attr( $color ) . '-300 hover:text-' . esc_attr( $color ) . '-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-' . esc_attr( $color ) . '-500 transition-all duration-200 touch-manipulation">';
            echo '<svg class="w-6 h-6 sm:w-5 sm:h-5 md:w-5 md:h-5 mr-3 sm:mr-2 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>';
            echo '</svg>';
            echo esc_html( $button_title );
            echo '</button>';
            echo '</div>';
        }

        /**
         * 渲染限制提示
         * 
         * @since 1.0
         */
        private function render_limit_alerts( $max, $min ) {
            if ( $max > 0 ) {
                echo '<div class="xun-repeater-alert xun-repeater-max-alert hidden mt-6 sm:mt-4 md:mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">';
                echo '<div class="flex">';
                echo '<div class="flex-shrink-0">';
                echo '<svg class="h-6 w-6 sm:h-5 sm:w-5 md:h-5 md:w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">';
                echo '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>';
                echo '</svg>';
                echo '</div>';
                echo '<div class="ml-3">';
                echo '<p class="text-base sm:text-sm md:text-sm text-yellow-700">已达到最大项目数量限制（' . $max . ' 个）</p>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
            }

            if ( $min > 0 ) {
                echo '<div class="xun-repeater-alert xun-repeater-min-alert hidden mt-6 sm:mt-4 md:mt-4 p-4 bg-red-50 border border-red-200 rounded-md">';
                echo '<div class="flex">';
                echo '<div class="flex-shrink-0">';
                echo '<svg class="h-6 w-6 sm:h-5 sm:w-5 md:h-5 md:w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">';
                echo '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>';
                echo '</svg>';
                echo '</div>';
                echo '<div class="ml-3">';
                echo '<p class="text-base sm:text-sm md:text-sm text-red-700">至少需要保留 ' . $min . ' 个项目</p>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
            }
        }

        /**
         * 渲染模板项目
         * 
         * @since 1.0
         */
        private function render_template_item( $fields, $collapsible, $show_preview, $preview_field, $color ) {
            echo '<div class="xun-repeater-template hidden" data-template>';
            echo '<div class="xun-repeater-item bg-white border border-gray-200 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md" data-index="{{INDEX}}">';
            
            // 模板项目头部
            echo '<div class="xun-repeater-item-header flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200 rounded-t-lg cursor-pointer" role="button" tabindex="0" aria-expanded="false" aria-label="展开或折叠此项目">';
            echo '<div class="flex items-center space-x-3">';
            echo '<div class="xun-repeater-sort-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors">';
            echo '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zM8 4h4v2H8V4zm0 4h4v2H8V8zm0 4h4v2H8v-2z"></path></svg>';
            echo '</div>';
            echo '<div class="flex-1"><div class="flex items-center space-x-2"><span class="xun-repeater-item-title text-sm font-medium text-gray-900" data-default-title="项目 #{{NUMBER}}">项目 #{{NUMBER}}</span></div></div>';
            echo '</div>';
            echo '<div class="flex items-center space-x-1">';
            if ( $collapsible ) {
                echo '<button type="button" class="xun-repeater-toggle inline-flex items-center p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors" title="折叠/展开" aria-expanded="false" aria-label="展开或折叠"><svg class="w-4 h-4 transform transition-transform rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button>';
            }
            echo '<button type="button" class="xun-repeater-clone inline-flex items-center p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors" title="复制项目"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button>';
            echo '<button type="button" class="xun-repeater-remove inline-flex items-center p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors" title="删除项目"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg></button>';
            echo '</div>';
            echo '</div>';
            
            // 模板项目内容
            echo '<div class="xun-repeater-item-content p-4 space-y-4" style="display: none;" aria-hidden="true">';
            foreach ( $fields as $field ) {
                $field_default = ( isset( $field['default'] ) ) ? $field['default'] : '';
                $field_unique = ( ! empty( $this->unique ) ) ? $this->unique .'['. $this->field['id'] .'][{{INDEX}}]' : $this->field['id'] .'[{{INDEX}}]';
                
                XUN::field( $field, $field_default, '___'. $field_unique, 'field/repeater' );
            }
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
        
        /**
         * 加载字段资源
         * 
         * 加载重复器字段所需的CSS和JavaScript文件。
         * 
         * @since 1.0
         */
        public function enqueue() {
            
            // 加载字段特定的脚本
            wp_enqueue_script( 
                'xun-field-repeater', 
                XUN_Setup::$url . '/assets/js/fields/repeater.js', 
                array( 'jquery', 'jquery-ui-sortable' ), 
                XUN_VERSION, 
                true 
            );
            
            // 本地化脚本
            wp_localize_script( 'xun-field-repeater', 'xunRepeater', array(
                'confirmDelete' => '确定要删除这个项目吗？',
                'confirmClear' => '确定要清空所有项目吗？此操作不可撤销。',
                'maxItems' => '已达到最大项目数量限制',
                'minItems' => '至少需要保留指定数量的项目',
                'addItem' => '添加项目',
                'removeItem' => '删除项目',
                'cloneItem' => '复制项目',
                'moveUp' => '上移',
                'moveDown' => '下移',
                'collapse' => '折叠',
                'expand' => '展开',
            ) );
        }
        
        /**
         * 验证重复器字段值
         * 
         * 验证重复器字段的数据结构和内容。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return mixed 验证后的值
         */
        public function validate( $value ) {
            
            if ( ! is_array( $value ) ) {
                return array();
            }
            
            $validated = array();
            $fields = ! empty( $this->field['fields'] ) ? $this->field['fields'] : array();
            
            foreach ( $value as $index => $item ) {
                if ( ! is_array( $item ) ) {
                    continue;
                }
                
                $validated_item = array();
                
                foreach ( $fields as $field ) {
                    if ( ! isset( $field['id'] ) ) {
                        continue;
                    }
                    
                    $field_value = isset( $item[$field['id']] ) ? $item[$field['id']] : '';
                    
                    // 应用字段特定的验证
                    if ( method_exists( $this, 'validate_field' ) ) {
                        $field_value = $this->validate_field( $field, $field_value );
                    }
                    
                    $validated_item[$field['id']] = $field_value;
                }
                
                $validated[] = $validated_item;
            }
            
            // 应用自定义验证过滤器
            $validated = apply_filters( 'xun_validate_repeater_field', $validated, $this->field );
            $validated = apply_filters( "xun_validate_repeater_field_{$this->field['id']}", $validated, $this->field );
            
            return $validated;
        }
        
        /**
         * 获取字段配置示例
         * 
         * 返回重复器字段的配置示例，用于文档和开发参考。
         * 
         * @since 1.0
         * 
         * @return array 配置示例数组
         */
        public static function get_config_example() {
            
            return array(
                'id'           => 'repeater_field_example',
                'type'         => 'repeater',
                'title'        => '重复器字段示例',
                'desc'         => '这是一个重复器字段的描述',
                'fields'       => array(
                    array(
                        'id'    => 'title',
                        'type'  => 'text',
                        'title' => '标题',
                    ),
                    array(
                        'id'    => 'content',
                        'type'  => 'textarea',
                        'title' => '内容',
                    ),
                ),
                'max'          => 10,
                'min'          => 1,
                'button_title' => '添加新项目',
                'sortable'     => true,
                'collapsible'  => true,
                'show_preview' => true,
                'preview_field' => 'title',
                'layout'       => 'default',
                'color'        => 'blue',
                'class'        => 'custom-class',
                'before'       => '前置内容',
                'after'        => '后置内容',
                'help'         => '帮助信息',
            );
        }
    }
}
