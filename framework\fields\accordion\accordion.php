<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 手风琴字段类型
 * 
 * 这个类实现了现代化的手风琴字段功能。
 * 提供比CSF更好的用户体验，包括流畅动画、无障碍访问、键盘导航等高级功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_accordion' ) ) {
    
    /**
     * XUN_Field_accordion 手风琴字段类 - 响应式自适应设计
     *
     * 提供现代化手风琴字段的完整功能，包括：
     * - 流畅的展开折叠动画
     * - 无障碍访问支持
     * - 键盘导航
     * - 自定义图标
     * - 多种展开模式
     * - 完全响应式设计（移动端优先）
     * - 触摸友好的交互体验
     * - 修复默认展开状态问题
     * - 跨设备兼容性优化
     * - 深色模式支持
     *
     * @since 1.0
     */
    class XUN_Field_accordion extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化手风琴字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染手风琴字段
         *
         * 输出现代化手风琴字段的HTML代码。
         *
         * @since 1.0
         */
        public function render() {

            // 检查必需的配置
            if ( empty( $this->field['accordions'] ) || ! is_array( $this->field['accordions'] ) ) {
                echo '<div class="bg-red-50 border border-red-200 rounded-md p-4">';
                echo '<div class="flex">';
                echo '<div class="flex-shrink-0">';
                echo '<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">';
                echo '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>';
                echo '</svg>';
                echo '</div>';
                echo '<div class="ml-3">';
                echo '<h3 class="text-sm font-medium text-red-800">配置错误</h3>';
                echo '<div class="mt-2 text-sm text-red-700">';
                echo '<p>手风琴字段缺少必需的 "accordions" 配置数组。</p>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                return;
            }

            // 获取字段配置
            $accordions = $this->field['accordions'];
            $multiple = ! empty( $this->field['multiple'] ) ? $this->field['multiple'] : false;
            $collapsible = ! empty( $this->field['collapsible'] ) ? $this->field['collapsible'] : true;
            $color = ! empty( $this->field['color'] ) ? $this->field['color'] : 'blue';
            $size = ! empty( $this->field['size'] ) ? $this->field['size'] : 'default';
            $rounded = ! empty( $this->field['rounded'] ) ? $this->field['rounded'] : true;
            $shadow = ! empty( $this->field['shadow'] ) ? $this->field['shadow'] : true;

            // 输出前置内容
            echo $this->field_before();

            // 开始手风琴容器 - 响应式自适应设计
            echo '<div class="xun-accordion-field w-full touch-manipulation" ';
            echo 'data-field-id="' . esc_attr( $this->field['id'] ) . '" ';
            echo 'data-multiple="' . esc_attr( $multiple ? 'true' : 'false' ) . '" ';
            echo 'data-collapsible="' . esc_attr( $collapsible ? 'true' : 'false' ) . '" ';
            echo 'data-color="' . esc_attr( $color ) . '" ';
            echo 'data-size="' . esc_attr( $size ) . '">';

            // 添加响应式CSS样式
            echo '<style>
                @media (max-width: 640px) {
                    .xun-accordion-field .xun-accordion-trigger {
                        padding: 1rem !important;
                        font-size: 1rem !important;
                        line-height: 1.5rem !important;
                    }
                    .xun-accordion-field .xun-accordion-content > div {
                        padding: 1rem !important;
                    }
                    .xun-accordion-field .xun-accordion-icon {
                        width: 1.5rem !important;
                        height: 1.5rem !important;
                    }
                }
                @media (min-width: 641px) and (max-width: 768px) {
                    .xun-accordion-field .xun-accordion-trigger {
                        padding: 0.875rem 1.25rem !important;
                        font-size: 0.9375rem !important;
                    }
                    .xun-accordion-field .xun-accordion-content > div {
                        padding: 0.875rem 1.25rem !important;
                    }
                }
                /* 修复默认展开状态 */
                .xun-accordion-field .xun-accordion-content.xun-accordion-open {
                    max-height: none !important;
                }
                .xun-accordion-field .xun-accordion-content:not(.max-h-0) {
                    max-height: none !important;
                }
                /* 移除选中边框效果 */
                .xun-accordion-field .xun-accordion-trigger:focus {
                    outline: none !important;
                    box-shadow: none !important;
                    border: none !important;
                }
                .xun-accordion-field .xun-accordion-trigger:focus-visible {
                    outline: none !important;
                    box-shadow: none !important;
                    border: none !important;
                }
            </style>';

            // 渲染手风琴项目
            foreach ( $accordions as $index => $accordion ) {
                $this->render_accordion_item( $accordion, $index, $multiple, $collapsible, $color, $size, $rounded, $shadow );
            }

            echo '</div>'; // 结束手风琴容器

            // 输出后置内容
            echo $this->field_after();
        }

        /**
         * 渲染单个手风琴项目
         * 
         * @since 1.0
         * 
         * @param array $accordion 手风琴项目配置
         * @param int   $index     项目索引
         * @param bool  $multiple  是否允许多个展开
         * @param bool  $collapsible 是否可折叠
         * @param string $color    颜色主题
         * @param string $size     尺寸
         * @param bool  $rounded   是否圆角
         * @param bool  $shadow    是否阴影
         */
        private function render_accordion_item( $accordion, $index, $multiple, $collapsible, $color, $size, $rounded, $shadow ) {
            
            // 检查必需的配置
            if ( empty( $accordion['title'] ) || empty( $accordion['fields'] ) ) {
                return;
            }

            $item_id = 'xun-accordion-item-' . $this->field['id'] . '-' . $index;
            $content_id = 'xun-accordion-content-' . $this->field['id'] . '-' . $index;
            $is_open = ! empty( $accordion['open'] ) ? $accordion['open'] : false;
            $icon = ! empty( $accordion['icon'] ) ? $accordion['icon'] : '';
            
            // 容器类名
            $container_classes = array(
                'xun-accordion-item',
                'border border-gray-200',
                'transition-all duration-200 ease-in-out'
            );
            
            if ( $rounded ) {
                $container_classes[] = $index === 0 ? 'rounded-t-lg' : '';
                $container_classes[] = 'last:rounded-b-lg';
            }
            
            if ( $shadow ) {
                $container_classes[] = 'shadow-sm hover:shadow-md';
            }
            
            if ( $index > 0 ) {
                $container_classes[] = '-mt-px'; // 重叠边框
            }

            echo '<div class="' . esc_attr( implode( ' ', array_filter( $container_classes ) ) ) . '" data-index="' . esc_attr( $index ) . '">';

            // 手风琴头部
            $this->render_accordion_header( $accordion, $index, $item_id, $content_id, $is_open, $icon, $color, $size );

            // 手风琴内容
            $this->render_accordion_content( $accordion, $index, $content_id, $item_id, $is_open );

            echo '</div>'; // 结束项目容器
        }

        /**
         * 渲染手风琴头部
         * 
         * @since 1.0
         */
        private function render_accordion_header( $accordion, $index, $item_id, $content_id, $is_open, $icon, $color, $size ) {
            
            // 头部尺寸类 - 响应式设计
            $size_classes = array(
                'small'   => 'px-4 py-3 sm:px-4 sm:py-3 md:px-4 md:py-3 text-base sm:text-sm md:text-sm',
                'default' => 'px-6 py-4 sm:px-6 sm:py-4 md:px-6 md:py-4 text-lg sm:text-base md:text-base',
                'large'   => 'px-8 py-5 sm:px-8 sm:py-5 md:px-8 md:py-5 text-xl sm:text-lg md:text-lg'
            );

            $header_size = isset( $size_classes[$size] ) ? $size_classes[$size] : $size_classes['default'];

            echo '<button type="button" ';
            echo 'class="xun-accordion-trigger w-full flex items-center justify-between ' . esc_attr( $header_size ) . ' bg-white hover:bg-gray-50 focus:outline-none transition-all duration-200 touch-manipulation" ';
            echo 'id="' . esc_attr( $item_id ) . '" ';
            echo 'aria-expanded="' . ( $is_open ? 'true' : 'false' ) . '" ';
            echo 'aria-controls="' . esc_attr( $content_id ) . '" ';
            echo 'data-target="' . esc_attr( $content_id ) . '" ';
            echo 'data-default-open="' . ( $is_open ? 'true' : 'false' ) . '">';

            // 左侧内容（图标和标题）- 响应式设计
            echo '<div class="flex items-center space-x-4 sm:space-x-3 md:space-x-3 min-w-0 flex-1">';

            // 自定义图标或默认图标 - 响应式尺寸
            if ( ! empty( $icon ) ) {
                if ( strpos( $icon, '<svg' ) !== false ) {
                    // SVG图标
                    echo '<div class="flex-shrink-0 text-' . esc_attr( $color ) . '-600 w-6 h-6 sm:w-5 sm:h-5 md:w-5 md:h-5">' . $icon . '</div>';
                } else {
                    // CSS类图标
                    echo '<div class="flex-shrink-0 text-' . esc_attr( $color ) . '-600 text-xl sm:text-lg md:text-lg"><i class="' . esc_attr( $icon ) . '"></i></div>';
                }
            }

            // 标题 - 响应式字体和截断
            echo '<span class="font-medium text-gray-900 text-left truncate">' . esc_html( $accordion['title'] ) . '</span>';

            echo '</div>';

            // 右侧展开指示器 - 响应式尺寸
            echo '<div class="flex-shrink-0 ml-2">';
            echo '<svg class="w-6 h-6 sm:w-5 sm:h-5 md:w-5 md:h-5 text-gray-500 transform transition-transform duration-200 xun-accordion-icon' . ( $is_open ? ' rotate-180' : '' ) . '" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>';
            echo '</svg>';
            echo '</div>';

            echo '</button>';
        }

        /**
         * 渲染手风琴内容
         * 
         * @since 1.0
         */
        private function render_accordion_content( $accordion, $index, $content_id, $item_id, $is_open ) {

            // 设置内容容器的类和样式 - 修复默认展开状态
            $content_classes = 'xun-accordion-content overflow-hidden transition-all duration-300 ease-in-out';
            $content_style = '';

            if ( $is_open ) {
                // 展开状态：添加特殊类，确保内容可见
                $content_classes .= ' xun-accordion-open';
                $content_style = 'max-height: none; opacity: 1;';
            } else {
                // 折叠状态：添加max-h-0类
                $content_classes .= ' max-h-0';
                $content_style = 'max-height: 0; opacity: 0;';
            }

            echo '<div class="' . esc_attr( $content_classes ) . '" ';
            echo 'style="' . esc_attr( $content_style ) . '" ';
            echo 'id="' . esc_attr( $content_id ) . '" ';
            echo 'aria-labelledby="' . esc_attr( $item_id ) . '" ';
            echo 'aria-hidden="' . ( $is_open ? 'false' : 'true' ) . '" ';
            echo 'role="region" ';
            echo 'data-default-open="' . ( $is_open ? 'true' : 'false' ) . '">';

            echo '<div class="px-6 py-4 sm:px-6 sm:py-4 md:px-6 md:py-4 bg-gray-50 border-t border-gray-200">';

            // 渲染字段
            if ( ! empty( $accordion['fields'] ) && is_array( $accordion['fields'] ) ) {
                
                // 不允许的字段类型（防止嵌套）
                $unallowed_types = array( 'accordion' );
                
                foreach ( $accordion['fields'] as $field ) {
                    
                    // 检查字段类型是否被允许
                    if ( in_array( $field['type'], $unallowed_types ) ) {
                        $field['_notice'] = true;
                    }
                    
                    $field_id = isset( $field['id'] ) ? $field['id'] : '';
                    $field_default = isset( $field['default'] ) ? $field['default'] : '';
                    $field_value = isset( $this->value[$field_id] ) ? $this->value[$field_id] : $field_default;
                    $unique_id = ! empty( $this->unique ) ? $this->unique . '[' . $this->field['id'] . ']' : $this->field['id'];
                    
                    // 使用XUN框架的字段渲染方法
                    XUN::field( $field, $field_value, $unique_id, 'field/accordion' );
                }
            }

            echo '</div>'; // 结束内容区域
            echo '</div>'; // 结束内容容器
        }
        
        /**
         * 加载字段资源
         * 
         * 加载手风琴字段所需的JavaScript文件。
         * 
         * @since 1.0
         */
        public function enqueue() {
            
            // 加载字段特定的脚本
            wp_enqueue_script( 
                'xun-field-accordion', 
                XUN_Setup::$url . '/assets/js/fields/accordion.js', 
                array( 'jquery' ), 
                XUN_VERSION, 
                true 
            );
            
            // 本地化脚本
            wp_localize_script( 'xun-field-accordion', 'xunAccordion', array(
                'expandText' => '展开',
                'collapseText' => '折叠',
                'expandAll' => '展开全部',
                'collapseAll' => '折叠全部',
            ) );
        }
        
        /**
         * 验证手风琴字段值
         * 
         * 验证手风琴字段的数据结构和内容。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return mixed 验证后的值
         */
        public function validate( $value ) {
            
            if ( ! is_array( $value ) ) {
                return array();
            }
            
            $validated = array();
            $accordions = ! empty( $this->field['accordions'] ) ? $this->field['accordions'] : array();
            
            foreach ( $accordions as $accordion ) {
                if ( empty( $accordion['fields'] ) ) {
                    continue;
                }
                
                foreach ( $accordion['fields'] as $field ) {
                    if ( ! isset( $field['id'] ) ) {
                        continue;
                    }
                    
                    $field_value = isset( $value[$field['id']] ) ? $value[$field['id']] : '';
                    
                    // 应用字段特定的验证
                    if ( method_exists( $this, 'validate_field' ) ) {
                        $field_value = $this->validate_field( $field, $field_value );
                    }
                    
                    $validated[$field['id']] = $field_value;
                }
            }
            
            // 应用自定义验证过滤器
            $validated = apply_filters( 'xun_validate_accordion_field', $validated, $this->field );
            $validated = apply_filters( "xun_validate_accordion_field_{$this->field['id']}", $validated, $this->field );
            
            return $validated;
        }
        
        /**
         * 获取字段配置示例
         * 
         * 返回手风琴字段的配置示例，用于文档和开发参考。
         * 
         * @since 1.0
         * 
         * @return array 配置示例数组
         */
        public static function get_config_example() {
            
            return array(
                'id'         => 'accordion_field_example',
                'type'       => 'accordion',
                'title'      => '手风琴字段示例',
                'desc'       => '这是一个手风琴字段的描述',
                'accordions' => array(
                    array(
                        'title'  => '基础设置',
                        'icon'   => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>',
                        'open'   => true,
                        'fields' => array(
                            array(
                                'id'    => 'site_title',
                                'type'  => 'text',
                                'title' => '网站标题',
                            ),
                            array(
                                'id'    => 'site_description',
                                'type'  => 'textarea',
                                'title' => '网站描述',
                            ),
                        ),
                    ),
                    array(
                        'title'  => '高级设置',
                        'fields' => array(
                            array(
                                'id'    => 'custom_css',
                                'type'  => 'textarea',
                                'title' => '自定义CSS',
                            ),
                        ),
                    ),
                ),
                'multiple'    => false,
                'collapsible' => true,
                'color'       => 'blue',
                'size'        => 'default',
                'rounded'     => true,
                'shadow'      => true,
                'class'       => 'custom-class',
                'before'      => '前置内容',
                'after'       => '后置内容',
                'help'        => '帮助信息',
            );
        }
    }
}
