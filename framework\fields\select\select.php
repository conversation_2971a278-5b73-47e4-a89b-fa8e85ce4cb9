<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 选择字段类型
 * 
 * 这个类实现了现代化的选择字段功能，提供比CSF更好的用户体验。
 * 支持单选、多选、搜索、分组等功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_select' ) ) {
    
    /**
     * XUN_Field_select 选择字段类
     * 
     * 提供现代化的选择字段功能，包括：
     * - 美观的下拉列表界面
     * - 键盘导航支持
     * - 搜索过滤功能
     * - 多选支持
     * - 分组选项支持
     * - 无障碍访问优化
     * - 响应式设计
     * 
     * @since 1.0
     */
    class XUN_Field_select extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化选择字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染选择字段
         * 
         * 输出现代化的选择字段HTML。
         * 
         * @since 1.0
         */
        public function render() {
            
            // 解析字段配置
            $args = wp_parse_args( $this->field, array(
                'placeholder'    => '请选择...',        // 占位符文字
                'multiple'       => false,             // 是否支持多选
                'searchable'     => true,              // 是否支持搜索
                'clearable'      => true,              // 是否可清空
                'max_height'     => 240,               // 下拉列表最大高度
                'show_count'     => false,             // 是否显示选项数量
                'select_all'     => false,             // 是否显示全选按钮（多选时）
                'close_on_select' => true,             // 选择后是否关闭下拉列表
                'options'        => array(),           // 选项数组
                'ajax'           => false,             // 是否使用AJAX加载选项
                'ajax_url'       => '',                // AJAX请求URL
                'min_search_length' => 0,              // 最小搜索长度
                'no_results_text' => '未找到匹配项',    // 无结果提示文字
                'loading_text'   => '加载中...',       // 加载提示文字
                'search_placeholder' => '搜索选项...',  // 搜索框占位符
            ) );
            
            // 处理值格式
            if ( $args['multiple'] ) {
                $this->value = is_array( $this->value ) ? $this->value : array_filter( (array) $this->value );
            } else {
                $this->value = is_array( $this->value ) ? reset( $this->value ) : $this->value;
            }
            
            // 处理选项
            $options = $this->get_field_options( $args );
            
            // 生成唯一ID
            $field_id = $this->field_id();
            $listbox_id = $field_id . '_listbox';
            
            // 输出字段前置内容
            echo $this->field_before();
            
            // 开始选择字段容器 - 添加响应式样式
            echo '<div class="xun-select-field w-full touch-manipulation" data-field-id="' . esc_attr( $field_id ) . '" data-multiple="' . ( $args['multiple'] ? 'true' : 'false' ) . '" data-searchable="' . ( $args['searchable'] ? 'true' : 'false' ) . '" data-clearable="' . ( $args['clearable'] ? 'true' : 'false' ) . '" data-close-on-select="' . ( $args['close_on_select'] ? 'true' : 'false' ) . '">';

            // 添加响应式CSS样式
            echo '<style>
                @media (max-width: 640px) {
                    .xun-select-field .xun-select-button {
                        min-height: 3rem !important;
                        padding: 0.75rem 1rem !important;
                        font-size: 1rem !important;
                        line-height: 1.5rem !important;
                    }
                    .xun-select-field .xun-select-dropdown {
                        max-height: 60vh !important;
                    }
                    .xun-select-field .xun-select-options {
                        max-height: 50vh !important;
                    }
                    .xun-select-field .xun-select-option {
                        padding: 0.75rem 1rem !important;
                        font-size: 1rem !important;
                    }
                    .xun-select-field .xun-select-search {
                        padding: 0.75rem 1rem !important;
                        font-size: 1rem !important;
                    }
                }
                @media (min-width: 641px) and (max-width: 768px) {
                    .xun-select-field .xun-select-button {
                        min-height: 2.5rem !important;
                        padding: 0.625rem 0.875rem !important;
                    }
                    .xun-select-field .xun-select-dropdown {
                        max-height: 50vh !important;
                    }
                }
            </style>';
            
            // 渲染选择按钮
            $this->render_select_button( $args, $field_id, $listbox_id, $options );
            
            // 渲染下拉列表
            $this->render_dropdown( $args, $listbox_id, $options );
            
            // 渲染隐藏的原生select（用于表单提交）
            $this->render_native_select( $args, $options );
            
            echo '</div>';
            
            // 输出字段后置内容
            echo $this->field_after();
        }
        
        /**
         * 渲染选择按钮
         * 
         * @since 1.0
         * 
         * @param array  $args       字段配置参数
         * @param string $field_id   字段ID
         * @param string $listbox_id 列表框ID
         * @param array  $options    选项数组
         */
        private function render_select_button( $args, $field_id, $listbox_id, $options ) {
            
            // 获取当前选中的显示文本
            $display_text = $this->get_display_text( $args, $options );
            
            echo '<div class="relative">';
            echo '<button type="button" aria-expanded="false" aria-haspopup="listbox" aria-labelledby="' . esc_attr( $field_id . '_label' ) . '" class="xun-select-button grid w-full cursor-default grid-cols-1 rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 sm:text-sm/6 touch-manipulation transition-all duration-200" data-listbox="' . esc_attr( $listbox_id ) . '">';
            
            // 显示文本区域 - 响应式右边距
            $right_padding = $args['clearable'] && ! empty( $this->value ) ? 'pr-12 sm:pr-10 md:pr-8' : 'pr-8 sm:pr-6 md:pr-6';
            echo '<span class="col-start-1 row-start-1 truncate xun-select-display ' . esc_attr( $right_padding ) . '">';
            if ( empty( $display_text ) ) {
                echo '<span class="text-gray-500">' . esc_html( $args['placeholder'] ) . '</span>';
            } else {
                echo esc_html( $display_text );
            }
            echo '</span>';
            
            // 下拉箭头 - 响应式尺寸
            echo '<svg viewBox="0 0 16 16" fill="currentColor" data-slot="icon" aria-hidden="true" class="col-start-1 row-start-1 w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4 self-center justify-self-end text-gray-500 xun-select-arrow">';
            echo '<path d="M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z" clip-rule="evenodd" fill-rule="evenodd" />';
            echo '</svg>';
            
            // 清空按钮（如果启用且有值）- 响应式设计
            if ( $args['clearable'] && ! empty( $this->value ) ) {
                echo '<button type="button" class="xun-select-clear absolute right-6 sm:right-5 md:right-4 top-1/2 -translate-y-1/2 w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 touch-manipulation" title="清空选择">';
                echo '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-3 h-3 sm:w-2.5 sm:h-2.5 md:w-2.5 md:h-2.5">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
                echo '</svg>';
                echo '</button>';
            }
            
            echo '</button>';
        }
        
        /**
         * 渲染下拉列表
         * 
         * @since 1.0
         * 
         * @param array  $args       字段配置参数
         * @param string $listbox_id 列表框ID
         * @param array  $options    选项数组
         */
        private function render_dropdown( $args, $listbox_id, $options ) {
            
            echo '<div class="xun-select-dropdown absolute z-10 mt-1 w-full bg-white shadow-lg ring-1 ring-black/5 rounded-md hidden transition-all duration-200" style="max-height: ' . esc_attr( $args['max_height'] ) . 'px;">';
            
            // 搜索框（如果启用）- 响应式设计
            if ( $args['searchable'] ) {
                echo '<div class="p-3 sm:p-2 md:p-2 border-b border-gray-100">';
                echo '<input type="text" class="xun-select-search w-full px-4 py-3 sm:px-3 sm:py-2 md:px-3 md:py-2 text-base sm:text-sm md:text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-150 touch-manipulation" placeholder="' . esc_attr( $args['search_placeholder'] ) . '">';
                echo '</div>';
            }
            
            // 全选按钮（多选时）- 响应式设计
            if ( $args['multiple'] && $args['select_all'] ) {
                echo '<div class="p-3 sm:p-2 md:p-2 border-b border-gray-100">';
                echo '<button type="button" class="xun-select-all w-full text-left px-4 py-3 sm:px-3 sm:py-2 md:px-3 md:py-2 text-base sm:text-sm md:text-sm text-blue-600 hover:bg-blue-50 rounded-md focus:outline-none focus:bg-blue-50 transition-colors duration-150 touch-manipulation">全选</button>';
                echo '</div>';
            }
            
            // 选项列表 - 响应式设计
            echo '<ul role="listbox" tabindex="-1" aria-labelledby="' . esc_attr( $listbox_id . '_label' ) . '" class="xun-select-options py-2 sm:py-1 md:py-1 overflow-auto" style="max-height: ' . esc_attr( $args['max_height'] - 100 ) . 'px;">';
            
            $this->render_options( $options, $args );
            
            echo '</ul>';
            
            // 无结果提示 - 响应式设计
            echo '<div class="xun-select-no-results hidden p-6 sm:p-4 md:p-4 text-center text-gray-500 text-base sm:text-sm md:text-sm">';
            echo esc_html( $args['no_results_text'] );
            echo '</div>';

            // 加载提示 - 响应式设计
            echo '<div class="xun-select-loading hidden p-6 sm:p-4 md:p-4 text-center text-gray-500 text-base sm:text-sm md:text-sm">';
            echo esc_html( $args['loading_text'] );
            echo '</div>';
            
            echo '</div>';
            echo '</div>'; // 关闭 relative 容器
        }
        
        /**
         * 渲染选项列表
         * 
         * @since 1.0
         * 
         * @param array $options 选项数组
         * @param array $args    字段配置参数
         */
        private function render_options( $options, $args ) {
            
            $option_index = 0;
            
            foreach ( $options as $option_key => $option_value ) {
                
                // 处理分组选项 - 响应式设计
                if ( is_array( $option_value ) ) {
                    echo '<li class="px-4 py-3 sm:px-3 sm:py-2 md:px-3 md:py-2 text-sm sm:text-xs md:text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50">';
                    echo esc_html( $option_key );
                    echo '</li>';
                    
                    foreach ( $option_value as $sub_key => $sub_value ) {
                        $this->render_single_option( $sub_key, $sub_value, $option_index, $args );
                        $option_index++;
                    }
                } else {
                    $this->render_single_option( $option_key, $option_value, $option_index, $args );
                    $option_index++;
                }
            }
        }
        
        /**
         * 渲染单个选项
         * 
         * @since 1.0
         * 
         * @param string $key    选项键
         * @param string $value  选项值
         * @param int    $index  选项索引
         * @param array  $args   字段配置参数
         */
        private function render_single_option( $key, $value, $index, $args ) {
            
            $is_selected = $args['multiple'] 
                ? in_array( $key, (array) $this->value ) 
                : $key == $this->value;
            
            // 响应式选项样式
            $selected_class = $is_selected ? 'bg-blue-600 text-white' : 'text-gray-900 hover:bg-gray-50';
            $font_weight = $is_selected ? 'font-semibold' : 'font-normal';

            echo '<li id="listbox-option-' . esc_attr( $index ) . '" role="option" aria-selected="' . ( $is_selected ? 'true' : 'false' ) . '" class="xun-select-option relative cursor-pointer py-3 px-4 sm:py-2 sm:px-3 md:py-2 md:px-3 pr-12 sm:pr-10 md:pr-9 select-none transition-colors duration-150 touch-manipulation ' . esc_attr( $selected_class ) . '" data-value="' . esc_attr( $key ) . '" data-text="' . esc_attr( $value ) . '">';

            echo '<span class="block truncate text-base sm:text-sm md:text-sm ' . esc_attr( $font_weight ) . '">' . esc_html( $value ) . '</span>';

            // 选中标记 - 响应式设计
            $checkmark_visibility = $is_selected ? '' : 'hidden';
            echo '<span class="absolute inset-y-0 right-0 flex items-center pr-4 sm:pr-3 md:pr-3 ' . esc_attr( $checkmark_visibility ) . '">';
            echo '<svg viewBox="0 0 20 20" fill="currentColor" data-slot="icon" aria-hidden="true" class="w-6 h-6 sm:w-5 sm:h-5 md:w-4 md:h-4">';
            echo '<path d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" fill-rule="evenodd" />';
            echo '</svg>';
            echo '</span>';
            
            echo '</li>';
        }

        /**
         * 渲染隐藏的原生select
         *
         * @since 1.0
         *
         * @param array $args    字段配置参数
         * @param array $options 选项数组
         */
        private function render_native_select( $args, $options ) {

            $multiple_attr = $args['multiple'] ? ' multiple' : '';
            $multiple_name = $args['multiple'] ? '[]' : '';

            echo '<select name="' . esc_attr( $this->field_name( $multiple_name ) ) . '" class="xun-select-native hidden"' . $multiple_attr . $this->field_attributes() . '>';

            if ( ! $args['multiple'] && empty( $this->value ) ) {
                echo '<option value=""></option>';
            }

            foreach ( $options as $option_key => $option_value ) {
                if ( is_array( $option_value ) ) {
                    echo '<optgroup label="' . esc_attr( $option_key ) . '">';
                    foreach ( $option_value as $sub_key => $sub_value ) {
                        $selected = $args['multiple']
                            ? ( in_array( $sub_key, (array) $this->value ) ? ' selected' : '' )
                            : ( $sub_key == $this->value ? ' selected' : '' );
                        echo '<option value="' . esc_attr( $sub_key ) . '"' . $selected . '>' . esc_html( $sub_value ) . '</option>';
                    }
                    echo '</optgroup>';
                } else {
                    $selected = $args['multiple']
                        ? ( in_array( $option_key, (array) $this->value ) ? ' selected' : '' )
                        : ( $option_key == $this->value ? ' selected' : '' );
                    echo '<option value="' . esc_attr( $option_key ) . '"' . $selected . '>' . esc_html( $option_value ) . '</option>';
                }
            }

            echo '</select>';
        }

        /**
         * 获取显示文本
         *
         * @since 1.0
         *
         * @param array $args    字段配置参数
         * @param array $options 选项数组
         *
         * @return string 显示文本
         */
        private function get_display_text( $args, $options ) {

            if ( empty( $this->value ) ) {
                return '';
            }

            if ( $args['multiple'] ) {
                $selected_count = count( (array) $this->value );
                if ( $selected_count === 0 ) {
                    return '';
                } elseif ( $selected_count === 1 ) {
                    $value = reset( $this->value );
                    return $this->get_option_text( $value, $options );
                } else {
                    return sprintf( '已选择 %d 项', $selected_count );
                }
            } else {
                return $this->get_option_text( $this->value, $options );
            }
        }

        /**
         * 获取选项文本
         *
         * @since 1.0
         *
         * @param string $value   选项值
         * @param array  $options 选项数组
         *
         * @return string 选项文本
         */
        private function get_option_text( $value, $options ) {

            foreach ( $options as $option_key => $option_value ) {
                if ( is_array( $option_value ) ) {
                    foreach ( $option_value as $sub_key => $sub_value ) {
                        if ( $sub_key == $value ) {
                            return $sub_value;
                        }
                    }
                } else {
                    if ( $option_key == $value ) {
                        return $option_value;
                    }
                }
            }

            return $value;
        }

        /**
         * 获取字段选项数组
         *
         * @since 1.0
         *
         * @param array $args 字段配置参数
         *
         * @return array 选项数组
         */
        public function get_field_options( $args ) {

            if ( ! empty( $args['ajax'] ) ) {
                // AJAX模式下返回空数组，选项将通过JavaScript加载
                return array();
            }

            if ( is_string( $args['options'] ) ) {
                // 如果options是字符串，尝试作为数据源处理
                // 目前暂不支持字符串数据源，直接返回空数组
                // 后续可以添加对posts、pages、categories等的支持
                return array();
            }

            return is_array( $args['options'] ) ? $args['options'] : array();
        }

        /**
         * 加载字段资源
         *
         * 加载选择字段所需的CSS和JavaScript文件。
         *
         * @since 1.0
         */
        public function enqueue() {

            // 加载字段专用JavaScript
            wp_enqueue_script(
                'xun-field-select',
                XUN_Setup::$url . '/assets/js/fields/select.js',
                array( 'jquery' ),
                XUN_VERSION,
                true
            );

            // 本地化脚本
            wp_localize_script( 'xun-field-select', 'xunSelectField', array(
                'no_results_text' => __( '未找到匹配项', 'xun' ),
                'loading_text'    => __( '加载中...', 'xun' ),
                'select_all_text' => __( '全选', 'xun' ),
                'clear_all_text'  => __( '清空', 'xun' ),
                'selected_text'   => __( '已选择 %d 项', 'xun' ),
            ) );
        }

        /**
         * 验证字段值
         *
         * 验证和清理选择字段值。
         *
         * @since 1.0
         *
         * @param mixed $value 要验证的值
         *
         * @return mixed 验证后的值
         */
        public function validate( $value ) {

            if ( empty( $value ) ) {
                return '';
            }

            // 获取字段配置
            $args = wp_parse_args( $this->field, array(
                'multiple' => false,
                'options'  => array(),
            ) );

            $options = $this->get_field_options( $args );
            $valid_keys = $this->get_valid_option_keys( $options );

            if ( $args['multiple'] ) {
                $value = is_array( $value ) ? $value : array( $value );
                $validated = array();

                foreach ( $value as $single_value ) {
                    if ( in_array( $single_value, $valid_keys ) ) {
                        $validated[] = sanitize_text_field( $single_value );
                    }
                }

                return $validated;
            } else {
                if ( in_array( $value, $valid_keys ) ) {
                    return sanitize_text_field( $value );
                }
                return '';
            }
        }

        /**
         * 获取有效的选项键
         *
         * @since 1.0
         *
         * @param array $options 选项数组
         *
         * @return array 有效的选项键数组
         */
        private function get_valid_option_keys( $options ) {

            $keys = array();

            foreach ( $options as $option_key => $option_value ) {
                if ( is_array( $option_value ) ) {
                    $keys = array_merge( $keys, array_keys( $option_value ) );
                } else {
                    $keys[] = $option_key;
                }
            }

            return $keys;
        }

        /**
         * 获取字段配置示例
         *
         * 返回选择字段的配置示例，用于文档和开发参考。
         *
         * @since 1.0
         *
         * @return array 配置示例数组
         */
        public static function get_config_example() {

            return array(
                'id'               => 'select_field_example',
                'type'             => 'select',
                'title'            => '选择字段示例',
                'desc'             => '从下拉列表中选择一个或多个选项',
                'placeholder'      => '请选择...',        // 占位符文字
                'multiple'         => false,             // 是否支持多选
                'searchable'       => true,              // 是否支持搜索
                'clearable'        => true,              // 是否可清空
                'max_height'       => 240,               // 下拉列表最大高度
                'show_count'       => false,             // 是否显示选项数量
                'select_all'       => false,             // 是否显示全选按钮（多选时）
                'close_on_select'  => true,              // 选择后是否关闭下拉列表
                'min_search_length' => 0,               // 最小搜索长度
                'no_results_text'  => '未找到匹配项',     // 无结果提示文字
                'loading_text'     => '加载中...',       // 加载提示文字
                'search_placeholder' => '搜索选项...',   // 搜索框占位符
                'options'          => array(
                    'option1' => '选项一',
                    'option2' => '选项二',
                    'option3' => '选项三',
                    'group1'  => array(
                        'sub1' => '分组选项一',
                        'sub2' => '分组选项二',
                    ),
                ),
                'default'          => '',                // 默认值
            );
        }
    }
}
