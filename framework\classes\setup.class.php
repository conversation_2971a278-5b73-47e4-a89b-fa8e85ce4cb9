<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 核心设置类
 * 
 * 这个类负责框架的初始化、配置管理和核心功能的设置。
 * 它是整个框架的入口点，管理所有其他组件的加载和初始化。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Setup' ) ) {
    
    /**
     * XUN_Setup 类
     * 
     * 框架的主要设置和初始化类，负责：
     * - 框架常量定义
     * - 文件包含管理
     * - 钩子注册
     * - 实例管理
     * 
     * @since 1.0
     */
    class XUN_Setup {
        
        /**
         * 框架版本号
         * 
         * @since 1.0
         * @var string
         */
        public static $version = '1.0';
        
        /**
         * 框架主文件路径
         * 
         * @since 1.0
         * @var string
         */
        public static $file = '';
        
        /**
         * 框架目录路径
         * 
         * @since 1.0
         * @var string
         */
        public static $dir = '';
        
        /**
         * 框架URL路径
         *
         * @since 1.0
         * @var string
         */
        public static $url = '';

        /**
         * 已注册的框架页面
         *
         * @since 1.0
         * @var array
         */
        public static $registered_pages = array();

        /**
         * 是否为高级版本
         * 
         * @since 1.0
         * @var bool
         */
        public static $premium = true;
        
        /**
         * 存储框架参数的数组
         * 
         * @since 1.0
         * @var array
         */
        public static $args = array(
            'admin_options'     => array(), // 后台选项配置
            'metabox_options'   => array(), // 元数据框选项配置
            'customize_options' => array(), // 自定义器选项配置
            'sections'          => array(), // 区块配置
        );
        
        /**
         * 已初始化的实例数组
         * 
         * @since 1.0
         * @var array
         */
        public static $inited = array();
        
        /**
         * 字段类型数组
         * 
         * @since 1.0
         * @var array
         */
        public static $fields = array();
        
        /**
         * 单例实例
         * 
         * @since 1.0
         * @var XUN_Setup|null
         */
        private static $instance = null;
        
        /**
         * 初始化框架
         * 
         * 这是框架的主要入口点，负责设置所有必要的常量、
         * 包含文件和初始化核心功能。
         * 
         * @since 1.0
         * 
         * @param string $file    框架主文件路径
         * @param bool   $premium 是否为高级版本
         * 
         * @return XUN_Setup 返回设置类实例
         */
        public static function init( $file = __FILE__, $premium = true ) {
            
            // 设置文件路径常量
            self::$file = $file;
            
            // 设置版本标识
            self::$premium = $premium;
            
            // 设置路径常量
            self::constants();
            
            // 包含必要文件
            self::includes();
            
            // 创建单例实例
            if ( is_null( self::$instance ) ) {
                self::$instance = new self();
            }
            
            return self::$instance;
        }
        
        /**
         * 构造函数
         * 
         * 初始化框架的核心功能，注册必要的WordPress钩子。
         * 
         * @since 1.0
         */
        public function __construct() {
            
            // 触发初始化动作钩子
            do_action( 'xun_init' );
            
            // 设置文本域
            self::textdomain();
            
            // 注册WordPress钩子
            add_action( 'after_setup_theme', array( 'XUN', 'setup' ) );
            add_action( 'init', array( 'XUN', 'setup' ) );
            add_action( 'switch_theme', array( 'XUN', 'setup' ) );
            add_action( 'admin_enqueue_scripts', array( 'XUN', 'add_admin_enqueue_scripts' ) );
            add_action( 'wp_enqueue_scripts', array( 'XUN', 'add_frontend_enqueue_scripts' ), 80 );
            add_action( 'wp_head', array( 'XUN', 'add_custom_css' ), 80 );
            add_filter( 'admin_body_class', array( 'XUN', 'add_admin_body_class' ) );

            // 初始化字段AJAX处理器
            add_action( 'init', array( $this, 'init_field_ajax_handlers' ) );
        }

        /**
         * 初始化字段AJAX处理器
         *
         * @since 1.0
         */
        public function init_field_ajax_handlers() {
            // 初始化gallery字段的AJAX处理器
            if ( class_exists( 'XUN_Field_gallery' ) ) {
                XUN_Field_gallery::init_ajax();
            }
        }

        /**
         * 设置框架常量
         * 
         * 定义框架运行所需的各种路径和URL常量。
         * 
         * @since 1.0
         */
        public static function constants() {
            
            // 获取框架目录路径
            // self::$file 是 xun-framework.php 的路径，在 framework 目录中
            // 所以 dirname(self::$file) 就是框架目录
            $dirname = str_replace( '//', '/', wp_normalize_path( dirname( self::$file ) ) );
            $theme_dir = str_replace( '//', '/', wp_normalize_path( get_parent_theme_file_path() ) );
            $plugin_dir = str_replace( '//', '/', wp_normalize_path( WP_PLUGIN_DIR ) );
            
            // 判断是否在插件目录中
            $located_plugin = ( preg_match( '#'. self::sanitize_dirname( $plugin_dir ) .'#', self::sanitize_dirname( $dirname ) ) ) ? true : false;
            $directory = ( $located_plugin ) ? $plugin_dir : $theme_dir;
            $directory_uri = ( $located_plugin ) ? WP_PLUGIN_URL : get_parent_theme_file_uri();
            $foldername = str_replace( $directory, '', $dirname );
            
            // 设置目录和URL常量
            self::$dir = $dirname;
            self::$url = $directory_uri . $foldername;
        }
        
        /**
         * 包含必要的文件
         *
         * 加载框架运行所需的所有类文件和函数文件。
         *
         * @since 1.0
         */
        public static function includes() {

            // 包含通用函数文件
            self::include_plugin_file( 'functions/helpers.php' );
            self::include_plugin_file( 'functions/sanitize.php' );
            self::include_plugin_file( 'functions/validate.php' );

            // 包含核心类文件
            self::include_plugin_file( 'classes/abstract.class.php' );
            self::include_plugin_file( 'classes/fields.class.php' );
            self::include_plugin_file( 'classes/admin-options.class.php' );
            self::include_plugin_file( 'classes/icons.class.php' );


        }
        
        /**
         * 设置文本域
         * 
         * 加载框架的多语言文件。
         * 
         * @since 1.0
         */
        public static function textdomain() {
            load_textdomain( 'xun', self::$dir . '/languages/' . get_locale() . '.mo' );
        }
        
        /**
         * 清理目录名称
         * 
         * 移除目录名称中的非字母字符，用于路径比较。
         * 
         * @since 1.0
         * 
         * @param string $dirname 目录名称
         * 
         * @return string 清理后的目录名称
         */
        public static function sanitize_dirname( $dirname ) {
            return preg_replace( '/[^A-Za-z]/', '', $dirname );
        }
        
        /**
         * 包含插件文件助手函数
         *
         * 安全地包含框架文件，支持主题覆盖功能。
         *
         * @since 1.0
         *
         * @param string $file 要包含的文件路径
         * @param bool   $load 是否立即加载文件
         *
         * @return string|void 如果不加载则返回文件路径
         */
        public static function include_plugin_file( $file, $load = true ) {

            $path = '';
            $file = ltrim( $file, '/' );
            $override = apply_filters( 'xun_override', 'xun-override' );

            // 检查主题覆盖文件
            if ( file_exists( get_parent_theme_file_path( $override . '/' . $file ) ) ) {
                $path = get_parent_theme_file_path( $override . '/' . $file );
            } elseif ( file_exists( get_theme_file_path( $override . '/' . $file ) ) ) {
                $path = get_theme_file_path( $override . '/' . $file );
            } elseif ( file_exists( self::$dir . '/' . $override . '/' . $file ) ) {
                $path = self::$dir . '/' . $override . '/' . $file;
            } elseif ( file_exists( self::$dir . '/' . $file ) ) {
                $path = self::$dir . '/' . $file;
            }

            // 加载文件或返回路径
            if ( ! empty( $path ) && ! empty( $file ) && $load ) {

                // 对于类文件，总是使用 require_once
                require_once( $path );

            } else {
                return self::$dir . '/' . $file;
            }
        }
    }
}

/**
 * XUN 主类
 *
 * 这是框架的主要接口类，提供了创建选项页面的静态方法。
 * 开发者主要通过这个类来使用框架功能。
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN' ) ) {

    /**
     * XUN 主类
     *
     * 提供框架的主要API接口，包括：
     * - 创建选项页面
     * - 创建区块
     * - 资源管理
     * - 工具函数
     *
     * @since 1.0
     */
    class XUN {

        /**
         * 创建选项页面
         *
         * 创建一个新的选项页面实例。
         *
         * @since 1.0
         *
         * @param string $id   唯一标识符
         * @param array  $args 选项页面参数
         *
         * @return XUN_Options 选项页面实例
         */
        public static function createOptions( $id, $args = array() ) {

            // 获取框架目录
            if ( ! empty( XUN_Setup::$dir ) ) {
                $framework_dir = XUN_Setup::$dir;
            } elseif ( defined( 'XUN_DIR' ) ) {
                $framework_dir = XUN_DIR;
            } else {
                // 从当前文件路径计算框架目录
                $framework_dir = dirname( dirname( __FILE__ ) );
            }



            // 确保所有必要的类都已加载
            if ( ! class_exists( 'XUN_Abstract' ) ) {
                $file_path = $framework_dir . '/classes/abstract.class.php';
                if ( file_exists( $file_path ) ) {
                    require_once $file_path;
                }
            }

            if ( ! class_exists( 'XUN_Fields' ) ) {
                $file_path = $framework_dir . '/classes/fields.class.php';
                if ( file_exists( $file_path ) ) {
                    require_once $file_path;
                }
            }

            if ( ! class_exists( 'XUN_Options' ) ) {
                $file_path = $framework_dir . '/classes/admin-options.class.php';
                if ( file_exists( $file_path ) ) {
                    require_once $file_path;
                }
            }

            $params = array(
                'args'     => $args,
                'sections' => array(),
            );

            XUN_Setup::$args['admin_options'][ $id ] = $params;

            // 立即创建实例并存储
            if ( ! isset( XUN_Setup::$inited[ $id ] ) ) {
                XUN_Setup::$inited[ $id ] = XUN_Options::instance( $id, $params );
            }

            return XUN_Setup::$inited[ $id ];
        }

        /**
         * 创建区块
         *
         * 为指定的选项页面创建一个新的区块。
         *
         * @since 1.0
         *
         * @param string $id      选项页面ID
         * @param array  $section 区块配置
         */
        public static function createSection( $id, $section ) {

            if ( isset( XUN_Setup::$args['admin_options'][ $id ] ) ) {
                XUN_Setup::$args['admin_options'][ $id ]['sections'][] = $section;

                // 如果实例已存在，更新其区块配置
                if ( isset( XUN_Setup::$inited[ $id ] ) ) {
                    XUN_Setup::$inited[ $id ]->sections = XUN_Setup::$args['admin_options'][ $id ]['sections'];
                    XUN_Setup::$inited[ $id ]->pre_sections = XUN_Setup::$inited[ $id ]->pre_sections( XUN_Setup::$inited[ $id ]->sections );
                    XUN_Setup::$inited[ $id ]->pre_fields = XUN_Setup::$inited[ $id ]->pre_fields( XUN_Setup::$inited[ $id ]->sections );
                }
            }
        }

        /**
         * 获取选项值
         *
         * 从指定的选项组中获取值。
         *
         * @since 1.0
         *
         * @param string $option_name 选项组名称
         * @param string $field_id    字段ID（可选）
         * @param mixed  $default     默认值
         *
         * @return mixed 选项值
         */
        public static function get_option( $option_name, $field_id = '', $default = '' ) {
            return xun_get_option( $option_name, $field_id, $default );
        }

        /**
         * 设置选项值
         *
         * 设置指定选项组中的值。
         *
         * @since 1.0
         *
         * @param string $option_name 选项组名称
         * @param string $field_id    字段ID
         * @param mixed  $value       要设置的值
         *
         * @return bool 是否设置成功
         */
        public static function set_option( $option_name, $field_id, $value ) {
            return xun_set_option( $option_name, $field_id, $value );
        }

        /**
         * 统一的字段工厂方法
         *
         * 这是框架的核心字段渲染方法，采用工厂模式统一创建和渲染所有字段类型。
         * 支持动态字段类型加载和第三方字段扩展。
         *
         * @since 1.0
         *
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置标识
         * @param string $parent 父级字段标识
         */
        public static function field( $field = array(), $value = '', $unique = '', $where = '', $parent = '' ) {

            // 检查字段配置的有效性
            if ( empty( $field ) || ! is_array( $field ) ) {
                echo '<div class="p-4 bg-red-50 border border-red-200 rounded-md">';
                echo '<p class="text-sm text-red-600">字段配置无效</p>';
                echo '</div>';
                return;
            }

            // 获取字段类型
            $field_type = ( ! empty( $field['type'] ) ) ? $field['type'] : '';

            if ( empty( $field_type ) ) {
                echo '<div class="p-4 bg-red-50 border border-red-200 rounded-md">';
                echo '<p class="text-sm text-red-600">字段类型未指定</p>';
                echo '</div>';
                return;
            }

            // 开始字段容器
            echo '<div class="xun-field xun-field-' . esc_attr( $field_type ) . '">';

            // 处理字段标题（仅在有标题且不是特殊字段类型时显示）
            $title_excluded_types = array( 'heading', 'content', 'notice', 'callback' );
            if ( ! empty( $field['title'] ) && ! in_array( $field_type, $title_excluded_types ) ) {
                echo '<div class="xun-title">';
                echo '<h4 class="text-sm font-medium text-gray-700 mb-2">' . esc_html( $field['title'] ) . '</h4>';
                if ( ! empty( $field['subtitle'] ) ) {
                    echo '<div class="xun-subtitle-text text-xs text-gray-500 mb-2">' . esc_html( $field['subtitle'] ) . '</div>';
                }
                echo '</div>';
            }

            // 开始字段内容容器
            echo ( ! empty( $field['title'] ) && ! in_array( $field_type, $title_excluded_types ) ) ? '<div class="xun-fieldset">' : '';

            // 处理默认值
            $value = ( ! isset( $value ) && isset( $field['default'] ) ) ? $field['default'] : $value;
            $value = ( isset( $field['value'] ) ) ? $field['value'] : $value;

            // 工厂模式核心：延迟加载并创建字段类实例
            $classname = 'XUN_Field_' . $field_type;

            // 尝试延迟加载字段类
            if ( ! class_exists( $classname ) ) {
                self::load_field_type( $field_type );
            }

            if ( class_exists( $classname ) ) {
                try {
                    // 创建字段实例
                    $instance = new $classname( $field, $value, $unique, $where, $parent );

                    // 延迟加载字段资源（如果方法存在）
                    if ( method_exists( $instance, 'enqueue' ) ) {
                        self::enqueue_field_assets( $field_type, $instance );
                    }

                    // 渲染字段
                    $instance->render();

                } catch ( Exception $e ) {
                    echo '<div class="p-4 bg-red-50 border border-red-200 rounded-md">';
                    echo '<p class="text-sm text-red-600">字段渲染错误: ' . esc_html( $e->getMessage() ) . '</p>';
                    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                        echo '<p class="text-xs text-red-400 mt-1">调试信息: ' . esc_html( $e->getFile() ) . ':' . esc_html( $e->getLine() ) . '</p>';
                    }
                    echo '</div>';
                }
            } else {
                // 字段类不存在的错误处理
                echo '<div class="p-4 bg-yellow-50 border border-yellow-200 rounded-md">';
                echo '<p class="text-sm text-yellow-600">字段类型 "' . esc_html( $field_type ) . '" 未找到</p>';
                echo '<p class="text-xs text-yellow-500 mt-1">请确保字段类 "' . esc_html( $classname ) . '" 已正确实现</p>';
                if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
                    $available_types = self::get_available_field_types();
                    echo '<p class="text-xs text-yellow-400 mt-1">可用字段类型: ' . esc_html( implode( ', ', $available_types ) ) . '</p>';
                }
                echo '</div>';
            }

            // 关闭字段内容容器
            echo ( ! empty( $field['title'] ) && ! in_array( $field_type, $title_excluded_types ) ) ? '</div>' : '';

            // 关闭字段容器
            echo '<div class="clear"></div>';
            echo '</div>';
        }

        /**
         * 框架设置
         *
         * 初始化框架的核心功能。
         *
         * @since 1.0
         */
        public static function setup() {

            // 不再预加载所有字段类型，改为按需加载
            // self::load_fields(); // 已移除预加载

            // 初始化选项页面
            self::init_options();
        }

        /**
         * 延迟加载字段类型
         *
         * 按需加载指定的字段类型，提高性能。
         *
         * @since 1.0
         *
         * @param string $field_type 字段类型名称
         *
         * @return bool 是否成功加载
         */
        public static function load_field_type( $field_type ) {

            // 性能监控开始
            $start_time = microtime( true );
            $start_memory = memory_get_usage();

            // 检查字段类是否已存在
            $classname = 'XUN_Field_' . $field_type;
            if ( class_exists( $classname ) ) {
                return true;
            }

            $loaded = false;

            // 检查是否已记录该字段文件路径
            if ( isset( self::$fields[ $field_type ] ) ) {
                require_once self::$fields[ $field_type ];
                $loaded = class_exists( $classname );
            } else {
                // 尝试加载字段文件
                $field_file = self::$dir . '/fields/' . $field_type . '/' . $field_type . '.php';

                if ( file_exists( $field_file ) ) {
                    require_once $field_file;
                    self::$fields[ $field_type ] = $field_file;
                    $loaded = class_exists( $classname );
                } else {
                    // 尝试从主题覆盖目录加载
                    $override = apply_filters( 'xun_override', 'xun-override' );
                    $theme_field_file = get_template_directory() . '/' . $override . '/fields/' . $field_type . '/' . $field_type . '.php';

                    if ( file_exists( $theme_field_file ) ) {
                        require_once $theme_field_file;
                        self::$fields[ $field_type ] = $theme_field_file;
                        $loaded = class_exists( $classname );
                    }
                }
            }

            // 记录性能数据
            if ( $loaded ) {
                $end_time = microtime( true );
                $end_memory = memory_get_usage();

                self::$performance_data['loaded_fields'][] = $field_type;
                self::$performance_data['load_times'][ $field_type ] = ( $end_time - $start_time ) * 1000; // 转换为毫秒
                self::$performance_data['memory_usage'][ $field_type ] = $end_memory - $start_memory;

                // 调试信息
                if ( defined( 'WP_DEBUG' ) && WP_DEBUG && defined( 'XUN_DEBUG_PERFORMANCE' ) && XUN_DEBUG_PERFORMANCE ) {
                    error_log( sprintf(
                        '[XUN Framework] 延迟加载字段 %s: %.2fms, %s bytes',
                        $field_type,
                        self::$performance_data['load_times'][ $field_type ],
                        number_format( self::$performance_data['memory_usage'][ $field_type ] )
                    ) );
                }
            }

            return $loaded;
        }

        /**
         * 获取可用字段类型列表
         *
         * 扫描字段目录，获取所有可用的字段类型（仅在需要时执行）。
         *
         * @since 1.0
         *
         * @return array 可用字段类型数组
         */
        public static function get_available_field_types() {

            static $available_types = null;

            if ( null !== $available_types ) {
                return $available_types;
            }

            $available_types = array();
            $fields_dir = self::$dir . '/fields';

            if ( is_dir( $fields_dir ) ) {
                $fields = glob( $fields_dir . '/*/');

                foreach ( $fields as $field_dir ) {
                    $field_name = basename( $field_dir );
                    $field_file = $field_dir . $field_name . '.php';

                    if ( file_exists( $field_file ) ) {
                        $available_types[] = $field_name;
                    }
                }
            }

            // 允许第三方添加字段类型
            $available_types = apply_filters( 'xun_available_field_types', $available_types );

            return $available_types;
        }

        /**
         * 已加载的字段资源记录
         *
         * @since 1.1.0
         * @var array
         */
        private static $enqueued_field_assets = array();

        /**
         * 性能监控数据
         *
         * @since 1.1.0
         * @var array
         */
        private static $performance_data = array(
            'loaded_fields' => array(),
            'load_times' => array(),
            'memory_usage' => array()
        );

        /**
         * 按需加载字段资源
         *
         * 避免重复加载相同字段类型的资源文件。
         *
         * @since 1.1.0
         *
         * @param string $field_type 字段类型
         * @param object $instance   字段实例
         */
        public static function enqueue_field_assets( $field_type, $instance ) {

            // 检查是否已加载该字段类型的资源
            if ( isset( self::$enqueued_field_assets[ $field_type ] ) ) {
                return;
            }

            // 标记为已加载
            self::$enqueued_field_assets[ $field_type ] = true;

            // 调用字段的资源加载方法
            if ( method_exists( $instance, 'enqueue' ) ) {
                $instance->enqueue();
            }

            // 触发字段资源加载钩子
            do_action( 'xun_field_enqueue_assets', $field_type, $instance );
        }

        /**
         * 获取当前页面使用的字段类型
         *
         * 分析当前页面配置，确定实际使用的字段类型。
         *
         * @since 1.1.0
         *
         * @return array 使用的字段类型数组
         */
        public static function get_current_page_field_types() {

            $used_types = array();

            // 获取当前页面的选项配置
            if ( isset( $_GET['page'] ) ) {
                $page_slug = sanitize_text_field( $_GET['page'] );

                // 遍历已注册的选项页面
                if ( isset( self::$args['admin_options'] ) ) {
                    foreach ( self::$args['admin_options'] as $option_id => $option_data ) {
                        if ( isset( $option_data['args']['menu_slug'] ) &&
                             $option_data['args']['menu_slug'] === $page_slug ) {

                            // 分析该页面的字段配置
                            if ( isset( $option_data['sections'] ) ) {
                                $used_types = array_merge( $used_types,
                                    self::extract_field_types_from_sections( $option_data['sections'] ) );
                            }
                        }
                    }
                }
            }

            return array_unique( $used_types );
        }

        /**
         * 从区块配置中提取字段类型
         *
         * 递归分析字段配置，提取所有使用的字段类型。
         *
         * @since 1.1.0
         *
         * @param array $sections 区块配置数组
         *
         * @return array 字段类型数组
         */
        private static function extract_field_types_from_sections( $sections ) {

            $types = array();

            foreach ( $sections as $section ) {
                if ( isset( $section['fields'] ) && is_array( $section['fields'] ) ) {
                    foreach ( $section['fields'] as $field ) {
                        if ( isset( $field['type'] ) ) {
                            $types[] = $field['type'];

                            // 处理嵌套字段（如 fieldset）
                            if ( isset( $field['fields'] ) && is_array( $field['fields'] ) ) {
                                $nested_types = self::extract_field_types_from_sections( array( array( 'fields' => $field['fields'] ) ) );
                                $types = array_merge( $types, $nested_types );
                            }
                        }
                    }
                }
            }

            return $types;
        }

        /**
         * 已加载的字段资源记录
         *
         * @since 1.1.0
         * @var array
         */
        private static $enqueued_field_assets = array();

        /**
         * 按需加载字段资源
         *
         * 避免重复加载相同字段类型的资源文件。
         *
         * @since 1.1.0
         *
         * @param string $field_type 字段类型
         * @param object $instance   字段实例
         */
        public static function enqueue_field_assets( $field_type, $instance ) {

            // 检查是否已加载该字段类型的资源
            if ( isset( self::$enqueued_field_assets[ $field_type ] ) ) {
                return;
            }

            // 标记为已加载
            self::$enqueued_field_assets[ $field_type ] = true;

            // 调用字段的资源加载方法
            if ( method_exists( $instance, 'enqueue' ) ) {
                $instance->enqueue();
            }

            // 触发字段资源加载钩子
            do_action( 'xun_field_enqueue_assets', $field_type, $instance );
        }

        /**
         * 获取当前页面使用的字段类型
         *
         * 分析当前页面配置，确定实际使用的字段类型。
         *
         * @since 1.1.0
         *
         * @return array 使用的字段类型数组
         */
        public static function get_current_page_field_types() {

            $used_types = array();

            // 获取当前页面的选项配置
            if ( isset( $_GET['page'] ) ) {
                $page_slug = sanitize_text_field( $_GET['page'] );

                // 遍历已注册的选项页面
                if ( isset( self::$args['admin_options'] ) ) {
                    foreach ( self::$args['admin_options'] as $option_id => $option_data ) {
                        if ( isset( $option_data['args']['menu_slug'] ) &&
                             $option_data['args']['menu_slug'] === $page_slug ) {

                            // 分析该页面的字段配置
                            if ( isset( $option_data['sections'] ) ) {
                                $used_types = array_merge( $used_types,
                                    self::extract_field_types_from_sections( $option_data['sections'] ) );
                            }
                        }
                    }
                }
            }

            return array_unique( $used_types );
        }

        /**
         * 从区块配置中提取字段类型
         *
         * 递归分析字段配置，提取所有使用的字段类型。
         *
         * @since 1.1.0
         *
         * @param array $sections 区块配置数组
         *
         * @return array 字段类型数组
         */
        private static function extract_field_types_from_sections( $sections ) {

            $types = array();

            foreach ( $sections as $section ) {
                if ( isset( $section['fields'] ) && is_array( $section['fields'] ) ) {
                    foreach ( $section['fields'] as $field ) {
                        if ( isset( $field['type'] ) ) {
                            $types[] = $field['type'];

                            // 处理嵌套字段（如 fieldset）
                            if ( isset( $field['fields'] ) && is_array( $field['fields'] ) ) {
                                $nested_types = self::extract_field_types_from_sections( array( array( 'fields' => $field['fields'] ) ) );
                                $types = array_merge( $types, $nested_types );
                            }
                        }
                    }
                }
            }

            return $types;
        }

        /**
         * 初始化选项页面
         *
         * 初始化所有已注册的选项页面。
         *
         * @since 1.0
         */
        public static function init_options() {

            if ( ! empty( XUN_Setup::$args['admin_options'] ) ) {
                foreach ( XUN_Setup::$args['admin_options'] as $key => $params ) {
                    if ( ! isset( XUN_Setup::$inited[ $key ] ) ) {
                        XUN_Setup::$inited[ $key ] = XUN_Options::instance( $key, $params );
                    }
                }
            }
        }

        /**
         * 添加管理页面脚本
         *
         * 在管理页面加载必要的CSS和JavaScript文件。
         *
         * @since 1.0
         */
        public static function add_admin_enqueue_scripts() {
            // 检查当前页面是否是框架页面
            $is_framework_page = false;

            // 检查是否在框架页面
            if ( isset( $_GET['page'] ) ) {
                $page = sanitize_text_field( wp_unslash( $_GET['page'] ) );

                // 检查页面是否以xun-开头，或者是否是已注册的框架页面
                if ( strpos( $page, 'xun-' ) === 0 || in_array( $page, XUN_Setup::$registered_pages ) ) {
                    $is_framework_page = true;
                }
            }

            // 只在框架页面加载CSS和JavaScript
            if ( $is_framework_page ) {
                // 确保加载WordPress的dashicons
                wp_enqueue_style( 'dashicons' );

                // 加载框架CSS
                wp_enqueue_style( 'xun-framework', XUN_Setup::$url . '/assets/css/style.min.css', array( 'dashicons' ), XUN_Setup::$version );

                // 加载框架JavaScript
                wp_enqueue_script( 'xun-framework', XUN_Setup::$url . '/assets/js/framework.min.js', array( 'jquery' ), XUN_Setup::$version, true );

                // 本地化脚本变量
                wp_localize_script( 'xun-framework', 'xun_vars', array(
                    'ajax_url' => admin_url( 'admin-ajax.php' ),
                    'nonce'    => wp_create_nonce( 'xun_nonce' ),
                    'i18n'     => array(
                        'confirm'     => __( '确定要执行此操作吗？', 'xun' ),
                        'saved'       => __( '设置已保存', 'xun' ),
                        'reset'       => __( '设置已重置', 'xun' ),
                        'error'       => __( '操作失败', 'xun' ),
                        'loading'     => __( '加载中...', 'xun' ),
                    ),
                ) );
            }
        }

        /**
         * 添加前端脚本
         *
         * 在前端加载必要的CSS文件。
         *
         * @since 1.0
         */
        public static function add_frontend_enqueue_scripts() {
            // 前端通常不需要加载框架脚本
            // 如果需要，可以在这里添加
        }

        /**
         * 添加自定义CSS
         *
         * 在页面头部添加自定义CSS样式。
         *
         * @since 1.0
         */
        public static function add_custom_css() {
            // 可以在这里添加动态生成的CSS
        }

        /**
         * 添加管理页面body类
         *
         * 为管理页面添加特定的CSS类。
         *
         * @since 1.0
         *
         * @param string $classes 现有的CSS类
         *
         * @return string 修改后的CSS类
         */
        public static function add_admin_body_class( $classes ) {

            if ( isset( $_GET['page'] ) && strpos( $_GET['page'], 'xun-' ) === 0 ) {
                $classes .= ' xun-framework-page';
            }

            return $classes;
        }

        /**
         * 获取图标HTML
         *
         * 获取指定名称的Heroicons图标HTML代码。
         *
         * @since 1.0
         *
         * @param string $name       图标名称（如：academic-cap）
         * @param array  $attributes 图标属性配置
         *
         * @return string 图标HTML代码
         */
        public static function icon( $name, $attributes = array() ) {
            return XUN_Icons::get_icon( $name, $attributes );
        }

        /**
         * 输出图标HTML
         *
         * 直接输出指定名称的图标HTML代码。
         *
         * @since 1.0
         *
         * @param string $name       图标名称
         * @param array  $attributes 图标属性配置
         */
        public static function the_icon( $name, $attributes = array() ) {
            echo self::icon( $name, $attributes );
        }

        /**
         * 检查图标是否存在
         *
         * 检查指定的图标是否存在。
         *
         * @since 1.0
         *
         * @param string $name  图标名称
         * @param string $size  图标尺寸
         * @param string $style 图标样式
         *
         * @return bool 图标是否存在
         */
        public static function icon_exists( $name, $size = '24', $style = 'outline' ) {
            return XUN_Icons::icon_exists( $name, $size, $style );
        }

        /**
         * 获取可用图标列表
         *
         * 获取所有可用的图标名称列表。
         *
         * @since 1.0
         *
         * @param string $size  图标尺寸
         * @param string $style 图标样式
         *
         * @return array 图标名称数组
         */
        public static function get_available_icons( $size = '24', $style = 'outline' ) {
            return XUN_Icons::get_available_icons( $size, $style );
        }

        /**
         * 搜索图标
         *
         * 根据关键词搜索匹配的图标。
         *
         * @since 1.0
         *
         * @param string $keyword 搜索关键词
         * @param string $size    图标尺寸
         * @param string $style   图标样式
         *
         * @return array 匹配的图标名称数组
         */
        public static function search_icons( $keyword, $size = '24', $style = 'outline' ) {
            return XUN_Icons::search_icons( $keyword, $size, $style );
        }

        /**
         * 获取性能监控数据
         *
         * 返回字段延迟加载的性能统计信息。
         *
         * @since 1.1.0
         *
         * @return array 性能数据
         */
        public static function get_performance_data() {
            return self::$performance_data;
        }

        /**
         * 输出性能报告
         *
         * 在调试模式下输出字段加载性能报告。
         *
         * @since 1.1.0
         */
        public static function output_performance_report() {

            if ( ! defined( 'WP_DEBUG' ) || ! WP_DEBUG || ! defined( 'XUN_DEBUG_PERFORMANCE' ) || ! XUN_DEBUG_PERFORMANCE ) {
                return;
            }

            $data = self::$performance_data;

            if ( empty( $data['loaded_fields'] ) ) {
                return;
            }

            $total_time = array_sum( $data['load_times'] );
            $total_memory = array_sum( $data['memory_usage'] );
            $field_count = count( $data['loaded_fields'] );

            echo '<div style="background: #f1f1f1; border: 1px solid #ccc; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px;">';
            echo '<h4 style="margin: 0 0 10px 0;">XUN Framework 性能报告</h4>';
            echo '<p><strong>延迟加载字段数量:</strong> ' . $field_count . '</p>';
            echo '<p><strong>总加载时间:</strong> ' . number_format( $total_time, 2 ) . 'ms</p>';
            echo '<p><strong>总内存使用:</strong> ' . number_format( $total_memory ) . ' bytes</p>';
            echo '<p><strong>平均加载时间:</strong> ' . number_format( $total_time / $field_count, 2 ) . 'ms</p>';

            echo '<details style="margin-top: 10px;">';
            echo '<summary>详细信息</summary>';
            echo '<table style="width: 100%; border-collapse: collapse; margin-top: 5px;">';
            echo '<tr style="background: #ddd;"><th style="padding: 5px; border: 1px solid #ccc;">字段类型</th><th style="padding: 5px; border: 1px solid #ccc;">加载时间</th><th style="padding: 5px; border: 1px solid #ccc;">内存使用</th></tr>';

            foreach ( $data['loaded_fields'] as $field_type ) {
                $time = isset( $data['load_times'][ $field_type ] ) ? number_format( $data['load_times'][ $field_type ], 2 ) . 'ms' : 'N/A';
                $memory = isset( $data['memory_usage'][ $field_type ] ) ? number_format( $data['memory_usage'][ $field_type ] ) . ' bytes' : 'N/A';

                echo '<tr>';
                echo '<td style="padding: 5px; border: 1px solid #ccc;">' . esc_html( $field_type ) . '</td>';
                echo '<td style="padding: 5px; border: 1px solid #ccc;">' . esc_html( $time ) . '</td>';
                echo '<td style="padding: 5px; border: 1px solid #ccc;">' . esc_html( $memory ) . '</td>';
                echo '</tr>';
            }

            echo '</table>';
            echo '</details>';
            echo '</div>';
        }
    }
}
