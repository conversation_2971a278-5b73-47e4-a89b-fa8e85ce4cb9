<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework - WordPress选项框架
 *
 * 这是一个轻量级的WordPress选项框架，专为主题和插件开发而设计。
 * 提供了简单而强大的方式来创建各种类型的选项面板和自定义字段。
 *
 * @package   Xun Framework - WordPress Options Framework
 * <AUTHOR> <<EMAIL>>
 * @link      https://www.xuntheme.com
 * @copyright 2025 Xun Theme
 * @version   1.0
 *
 * Plugin Name: Xun Framework
 * Plugin URI: https://www.xuntheme.com/
 * Author: June
 * Author URI: https://www.xuntheme.com/
 * Version: 1.0
 * Description: 一个简单轻量的WordPress选项框架，适用于主题和插件开发
 * Text Domain: xun
 * Domain Path: /languages
 *
 * @since 1.0
 */

// 防止直接访问
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * 定义框架版本常量
 *
 * @since 1.0
 */
if ( ! defined( 'XUN_VERSION' ) ) {
    define( 'XUN_VERSION', '1.0' );
}

/**
 * 定义框架主文件路径常量
 *
 * @since 1.0
 */
if ( ! defined( 'XUN_FILE' ) ) {
    define( 'XUN_FILE', __FILE__ );
}

/**
 * 定义框架目录路径常量
 *
 * @since 1.0
 */
if ( ! defined( 'XUN_DIR' ) ) {
    define( 'XUN_DIR', plugin_dir_path( __FILE__ ) );
}



/**
 * 加载调试配置
 *
 * @since 1.1.0
 */
if ( file_exists( XUN_DIR . 'config/debug.php' ) ) {
    require_once XUN_DIR . 'config/debug.php';
}

/**
 * 引入主要的设置类文件
 *
 * @since 1.0
 */
require_once XUN_DIR . 'classes/setup.class.php';

/**
 * 初始化框架
 *
 * 启动Xun Framework的核心功能，包括：
 * - 加载必要的类文件
 * - 注册WordPress钩子
 * - 初始化字段类型
 * - 设置多语言支持
 *
 * @since 1.0
 */
XUN_Setup::init( __FILE__, true );
