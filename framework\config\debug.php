<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问

/**
 * Xun Framework 调试配置
 *
 * 用于配置框架的调试和性能监控功能。
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.1.0
 */

// 性能调试开关
// 在 wp-config.php 中定义 define( 'XUN_DEBUG_PERFORMANCE', true ); 来启用
if ( ! defined( 'XUN_DEBUG_PERFORMANCE' ) ) {
    define( 'XUN_DEBUG_PERFORMANCE', false );
}

// 字段加载调试开关
if ( ! defined( 'XUN_DEBUG_FIELDS' ) ) {
    define( 'XUN_DEBUG_FIELDS', false );
}

// 资源加载调试开关
if ( ! defined( 'XUN_DEBUG_ASSETS' ) ) {
    define( 'XUN_DEBUG_ASSETS', false );
}

/**
 * 调试日志函数
 *
 * 统一的调试日志记录函数。
 *
 * @since 1.1.0
 *
 * @param string $level   日志级别 (info, warning, error, debug)
 * @param string $message 日志消息
 * @param array  $context 上下文数据
 */
function xun_debug_log( $level, $message, $context = array() ) {
    
    if ( ! defined( 'WP_DEBUG' ) || ! WP_DEBUG ) {
        return;
    }

    $log_message = sprintf( 
        '[XUN Framework] [%s] %s', 
        strtoupper( $level ), 
        $message 
    );

    if ( ! empty( $context ) ) {
        $log_message .= ' ' . wp_json_encode( $context );
    }

    error_log( $log_message );
}

/**
 * 性能监控辅助函数
 *
 * 简化性能监控的使用。
 *
 * @since 1.1.0
 */
class XUN_Performance_Monitor {

    /**
     * 监控数据
     *
     * @var array
     */
    private static $monitors = array();

    /**
     * 开始监控
     *
     * @param string $name 监控名称
     */
    public static function start( $name ) {
        self::$monitors[ $name ] = array(
            'start_time'   => microtime( true ),
            'start_memory' => memory_get_usage(),
        );
    }

    /**
     * 结束监控
     *
     * @param string $name 监控名称
     *
     * @return array 监控结果
     */
    public static function end( $name ) {
        
        if ( ! isset( self::$monitors[ $name ] ) ) {
            return false;
        }

        $monitor = self::$monitors[ $name ];
        $result = array(
            'execution_time' => ( microtime( true ) - $monitor['start_time'] ) * 1000, // 毫秒
            'memory_usage'   => memory_get_usage() - $monitor['start_memory'], // 字节
            'peak_memory'    => memory_get_peak_usage(),
        );

        unset( self::$monitors[ $name ] );

        if ( XUN_DEBUG_PERFORMANCE ) {
            xun_debug_log( 'performance', "Monitor '{$name}' completed", $result );
        }

        return $result;
    }

    /**
     * 获取所有监控数据
     *
     * @return array
     */
    public static function get_all_monitors() {
        return self::$monitors;
    }
}

/**
 * 字段加载性能监控
 *
 * 在字段加载时自动记录性能数据。
 *
 * @since 1.1.0
 */
if ( XUN_DEBUG_FIELDS ) {
    add_action( 'xun_field_before_load', function( $field_type ) {
        XUN_Performance_Monitor::start( "field_load_{$field_type}" );
    } );

    add_action( 'xun_field_after_load', function( $field_type ) {
        $result = XUN_Performance_Monitor::end( "field_load_{$field_type}" );
        if ( $result ) {
            xun_debug_log( 'debug', "Field '{$field_type}' loaded", $result );
        }
    } );
}

/**
 * 资源加载性能监控
 *
 * 监控CSS和JS资源的加载性能。
 *
 * @since 1.1.0
 */
if ( XUN_DEBUG_ASSETS ) {
    add_action( 'wp_enqueue_scripts', function() {
        XUN_Performance_Monitor::start( 'frontend_assets' );
    }, 1 );

    add_action( 'admin_enqueue_scripts', function() {
        XUN_Performance_Monitor::start( 'admin_assets' );
    }, 1 );

    add_action( 'wp_footer', function() {
        $result = XUN_Performance_Monitor::end( 'frontend_assets' );
        if ( $result ) {
            xun_debug_log( 'debug', 'Frontend assets loaded', $result );
        }
    }, 999 );

    add_action( 'admin_footer', function() {
        $result = XUN_Performance_Monitor::end( 'admin_assets' );
        if ( $result ) {
            xun_debug_log( 'debug', 'Admin assets loaded', $result );
        }
    }, 999 );
}
